package shared.sdsautoparser.utils

import ai.prevalent.sdsautoparser.parserimpl.regex.keyvalue.RegexKeyValueParser
import ai.prevalent.sdsautoparser.utils.RegexUtils.{convertArrayToMap, getKeyValueFieldMap, getRegexExpression}
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec

class RegexUtilsSpec extends AnyFlatSpec with DataFrameSuiteBase with BeforeAndAfter {

  var expected1: Map[String, String] = _
  var splitter1: String = _
  before {
    expected1 = Map("key1" -> "value1", "key2" -> "value2")
    splitter1 = ":"
  }

  "convertArrayToMap" should "convert given array to map" in {
    val inputArray = Array("key1:value1", "key2:value2")
    val output = convertArrayToMap(inputArray, splitter1)
    assert(expected1 === output)
  }
  "convertArrayToMap" should "convert given array to map with equal to splitter" in {
    val input_Array = Array("key3=value3", "key4=value4")
    val splitter_2 = "="
    val output_2 = convertArrayToMap(input_Array, splitter_2)
    val expected=Map("key3" -> "value3", "key4" -> "value4")
    assert(expected === output_2)
  }
  "convertArrayToMap" should "return null when empty array is passed" in {
    val input_Array_2 = Array.empty[String]
    assert(convertArrayToMap(input_Array_2, splitter1) === null)
  }
  "convertArrayToMap" should "handle special characters in the value" in {
    val input_Array_3 = Array("key1:value1_4", "key2:valu*e/2@:")
    val expected = Map("key1" -> "value1_4", "key2" -> "valu*e/2@:")
    assert(convertArrayToMap(input_Array_3, splitter1) === expected)
  }
  "convertArrayToMap" should "not split when invalid splitter is provided" in {
    val inputArray = Array("key5:value5", "key6:value6")
    val expected_2 = Map("key5:value5" -> "", "key6:value6" -> "")
    val splitter_2 = "="
    assert(convertArrayToMap(inputArray, splitter_2) === expected_2)
  }
  "convertArrayToMap" should "not split when a value doesnt contain contain splitter" in {
    val input_Array_4 = Array("ab", "c:d")
    val splitter_4 = ":"
    val expected = Map("ab" -> "", "c" -> "d")
    assert(expected === convertArrayToMap(input_Array_4, splitter_4))
  }
  "convertArrayToMap" should "correctly handle empty strings" in {
    val input_Array_5 = Array("", "e:f")
    val splitter_5 = ":"
    val expected_5 = Map("" -> "", "e" -> "f")
    assert(convertArrayToMap(input_Array_5, splitter_5) === expected_5)
  }
  "convertArrayToMap" should "handle duplicate keys" in {
    val input_Array_6 = Array("name:ann", "name:peter")
    val splitter_6 = ":"
    val expected = Map("name" -> "peter")
    assert(expected === convertArrayToMap(input_Array_6, splitter_6))
  }
  "convertArrayToMap" should "handle leading and trailing white spaces in the input string" in {
    //handle leading and trailing white spaces in the input string
    val input_Array_7 = Array("id: 465 ", "place:   Kolkata ")
    val splitter_7 = ":"
    val expected = Map("id" -> "465", "place" -> "Kolkata")
    assert(expected === convertArrayToMap(input_Array_7, splitter_7))
  }
  "convertArrayToMap" should "handle splitter in the value" in {
    val input_Array = Array("number:345", "cve:12345:26348234:990")
    val expected = Map("number" -> "345", "cve" -> "12345:26348234:990")
    assert(expected === convertArrayToMap(input_Array, splitter1))
  }
  "convertArrayToMap" should "handle quotes in the value" in {
    val input_Array = Array("os:\"Windows\"", "hostmane:xyz@corp")
    val expected = Map("os" -> "Windows", "hostmane" -> "xyz@corp")
    assert(convertArrayToMap(input_Array, splitter1) === expected)
  }

  "getRegexExpression" should "return the field specific regex" in {
    val field_regex = "[0-9]"
    val global_regex = "[a-z]"
    assert(getRegexExpression(field_regex, global_regex) === field_regex)
  }
  "getRegexExpression" should "return the global regex if field specific is null" in {
    val field_regex: Null = null
    val global_regex = "[A-Z]"
    assert(getRegexExpression(field_regex, global_regex) === global_regex)
  }
  "getRegexExpression" should "return the field regex if field specific is empty" in {
    val field_regex = ""
    val global_regex = "[\\d+]"
    assert(getRegexExpression(field_regex, global_regex) === field_regex)
  }
  "getRegexExpression" should "return the null if both the regex are null" in {
    val field_regex: Null = null
    val global_regex: Null = null
    assert(getRegexExpression(field_regex, global_regex) === null)
  }
  "getKeyValueFieldMap" should "give a map of subfields with their types" in {
    val path1 = getClass.getResource("/autoparserbase/input/config1.json").getPath
    val params1 = Array("--config", s"file:$path1", "--spark-service", "spark", "--modified-before", "2023-06-07 12:00:00", "--modified-after", "2023-06-07 11:00:00","--rdm-path","s3a://sds-qa-datalake/ingestion/rdm/qualys/host_summary/","--srdm-table","srdm.qualys__host_summary")
    val config1 = RegexKeyValueParser.getConfig(spark, RegexKeyValueParser.getParseCmdArgs(params1))
    val expected = "LinkedHashMap(host_id -> (data,SubFieldConfig(HOST_ID,string)), host_ip -> (data,SubFieldConfig(IP,long)), last_scan_datetime -> (data,SubFieldConfig(LAST_SCAN_DATETIME,timestamp)), name -> (msg,SubFieldConfig(NAME,string)), position -> (msg,SubFieldConfig(POSITION,string)), vpn -> (msg,SubFieldConfig(VPN,long)))"
    assert(expected === getKeyValueFieldMap(config1.keyValueFields).toString())


  }


}

