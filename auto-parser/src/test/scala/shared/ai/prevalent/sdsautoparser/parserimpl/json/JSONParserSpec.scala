package shared.ai.prevalent.sdsautoparser.parserimpl.json

import ai.prevalent.sdsautoparser.parserimpl.json.{<PERSON><PERSON><PERSON><PERSON><PERSON>, JSONParserConfig}
import ai.prevalent.sdspecore.test.utils.SharedSessionTestTrait
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.functions.col
import org.apache.spark.sql.types.{DataType, StructType}
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec

class JSONParserSpec extends AnyFlatSpec with SharedSessionTestTrait with BeforeAndAfter {

  var dfSql: String = _
  var commonParams: Array[String] = _
  var config: JSONParserConfig = _
  var path: String = _
  var schema: StructType = _
  var inputPaths: Array[(String, String)] = _
  var configs: Map[String, JSONParserConfig] = _
  var schemaParams: Array[String] = _

  private val sourcePath = "/parserimpl/json/"
  private val baseInputPath = sourcePath + "input/"
  private val baseOutputPath = sourcePath + "output/"
  private val baseInputPath2 = sourcePath + "metadatatransformation/input/"
  before {
    dfSql = "{\"type\":\"struct\",\"fields\":[{\"name\":\"payload\",\"type\":\"string\",\"nullable\":true}]}"
    path = getClass.getResource(baseInputPath + "config.json").getPath
    val rdmPath = "s3a://pai-sol-dev-datalake/autoparser/ingestion/rdm/nexus_rsa_syslog/"
    val srdmName = "srdm.defender_device_events"
    val paramsConfig = Array("--config", s"file:$path")
    commonParams = Array("--spark-service", "spark", "--modified-before", "2023-06-07 12:00:00",
      "--modified-after", "2023-06-07 11:00:00","--rdm-path", rdmPath,"--srdm-table", srdmName)
    config = JSONParser.getConfig(spark, JSONParser.getParseCmdArgs(paramsConfig ++ commonParams))
    inputPaths = Array("struct_as_string_schema.json", "custom_schema.json", "nested_custom_schema.json",
      "schema_merge.json", "struct_diff_schema.json", "struct_diff_schema_disabled.json",
      "array_struct_schema.json", "array_struct_schema_disabled.json").map(item=> (item, baseInputPath)) ++
      Array("source_type_level2.json", "source_type_level3.json", "source_type_level4.json")
        .map(item => (item, baseInputPath2 + "configs/"))
    val schemaParams = commonParams ++ Array("--config-path")
    configs = inputPaths.map { inputPath =>
      val schemaPath = getClass.getResource(s"${inputPath._2}${inputPath._1}").getPath
      val testConfig = JSONParser.getConfig(spark, JSONParser.getParseCmdArgs(schemaParams :+ s"file:$schemaPath"))
      (inputPath._1, testConfig)
    }.toMap
    dfSql = "{\"type\":\"struct\",\"fields\":[{\"name\":\"payload\",\"type\":\"string\",\"nullable\":true}]}"
    schema = DataType.fromJson(dfSql).asInstanceOf[StructType]
  }

  private def readInput(path: String, schema: StructType) = {
    spark.read.schema(schema).json(getClass.getResource(path).getPath)
  }

  private def readInputWithoutSchema(path: String) = {
    spark.read.json(getClass.getResource(path).getPath)
  }

  private def runParseAndCompare(sourceFile: String, outputPath: String, config: JSONParserConfig, orderSensitive: Boolean = false): Unit = {
    val df = readInput(baseInputPath + sourceFile, schema)
    val expected = readInputWithoutSchema(baseOutputPath + outputPath)
    val outputDF = parseJsonConfig(df, config)
    if (orderSensitive)
      assertDataFrameDataEquals(outputDF.orderBy(expected.columns.map(col): _*), expected.orderBy(expected.columns.map(col): _*))
    else
      assertDataFrameDataEquals(outputDF, expected)
  }

  private def runParseAndCompare2(path: String, outputSchema: String, expectedSchema: String): Unit = {
    val df = readInputWithoutSchema(baseInputPath2 + path)
    val config = configs(path)
    assert(parseJsonConfig(df, config).select(outputSchema).schema.simpleString == expectedSchema)
  }

  private def parseJsonConfig(df: DataFrame, config: JSONParserConfig): DataFrame = {
    JSONParser.parse(spark, df, config)
  }

  "it" should "parse json data properly" in {
    runParseAndCompare("input.json", "expected.json", config)
  }
  "it" should "not parse invalid json data" in {
    assertResult(1)(parseJsonConfig(readInput(baseInputPath + "invalid.json", schema), config)
      .select("_corrupt_record").filter("_corrupt_record is not null").count())
  }
  "it" should "parse nested arrays" in {
    runParseAndCompare("nested_array.json",  "nested_array.json", config)
  }

  "it" should "parse nested struct" in {
    runParseAndCompare("nested_structure.json",  "nested_structure.json",
      config, orderSensitive = true)
  }

  "it" should "parse case sensitive json data" in {
    spark.conf.set("spark.sql.caseSensitive", "true")
    runParseAndCompare("case_sensitive.json",  "case_sensitive.json", config)
    spark.conf.unset("spark.sql.caseSensitive")
  }

  "it" should "parse data with special characters in  key and value" in {
    runParseAndCompare("special_character.json",  "special_character.json", config)
  }

  "it" should "parse data with different data types" in {
    runParseAndCompare("data_types.json",  "data_types.json",
      config, orderSensitive = true)
  }


  "it" should "parse json data with struct field as string" in {
    runParseAndCompare("struct_as_string_input.json",  "struct_as_string_output.json", configs("struct_as_string_schema.json"))
  }

  "it" should "parse json data with type explicitly from schema" in {
    runParseAndCompare("custom_schema_input.json",  "custom_schema_output.json", configs("custom_schema.json"))
  }

  "it" should "parse json from schema with nested fields only from schema" in {
    runParseAndCompare("nested_custom_schema_input.json",  "nested_custom_schema_output.json", configs("nested_custom_schema.json"))

  }

  "it" should "parse json by merging inferred schema and custom schema" in {
    runParseAndCompare("schema_merge_input.json",  "schema_merge_output.json", configs("schema_merge.json"))
  }

  "it" should "parse json data by iterating through struct type when inferStructSchema is enabled" in {
    runParseAndCompare("struct_diff_input.json",  "struct_diff_output.json", configs("struct_diff_schema.json"))
  }

  "it" should "parse json data without iterating through struct when inferStructSchema is disabled" in {
    runParseAndCompare("struct_diff_input.json",  "struct_schema_diff_disabled.json", configs("struct_diff_schema_disabled.json"))
  }

  "it" should "parse json data by iterating through array of struct when inferStructSchema is enabled" in {
    runParseAndCompare("array_struct_schema_input.json",  "array_struct_output.json", configs("array_struct_schema.json"))

  }

  "it" should "parse json data without inferring schema from array of struct type when inferStructSchema is disabled" in {
    runParseAndCompare("array_struct_schema_input.json",  "array_struct_disabled_output.json",  configs("array_struct_schema_disabled.json")  )
  }

  "it" should "transform with level3 nested data with metadata transformation" in {
    runParseAndCompare2("source_type_level3.json",
      "nested_json_level2.Constant.nested_field.nested_field_level2_updated", "struct<nested_field_level2_updated:array<map<string,string>>>")
  }

  "it" should "transform nested data with level2 metadata transformation" in {
    runParseAndCompare2("source_type_level2.json",
      "nested_json_level2.Constant_updated", "struct<Constant_updated:array<map<string,string>>>")
  }

  "it" should "do metadata transformation with level4" in {
    runParseAndCompare2("source_type_level4.json",
      "nested_json_level.Constant.struct_field.nested_field_level4.nested_field_updated", "struct<nested_field_updated:array<map<string,string>>>")
  }

}


