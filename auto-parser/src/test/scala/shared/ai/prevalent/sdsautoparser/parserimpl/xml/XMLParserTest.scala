package shared.sdsautoparser.parserimpl.xml

import ai.prevalent.sdsautoparser.parserimpl.xml.{XMLParser, XMLParserConfig}
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.apache.spark.SparkConf
import org.apache.spark.sql.functions.col
import org.apache.spark.sql.types.{DataType, StructType}
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec


class XMLParserTest extends AnyFlatSpec with DataFrameSuiteBase with BeforeAndAfter {
  var config1: XMLParserConfig = _
  var config2: XMLParserConfig = _
  var config3: XMLParserConfig = _
  var schema: StructType = _
  var dfSql: String = _
  var configs: Map[String, XMLParserConfig] = _
  var configPaths: List[String] = _
  var inputFiles: List[String] = _
  var params: Array[String] = _
  before {
    dfSql = "{\"type\":\"struct\",\"fields\":[{\"name\":\"payload\",\"type\":\"string\",\"nullable\":true}]}"
    schema = DataType.fromJson(dfSql).asInstanceOf[StructType]
    inputFiles = List("config1.json", "basic_config.json", "config2.json")
    params = Array("--spark-service", "spark","--rdm-path", "s3a://pai-sol-dev-datalake/autoparser/ingestion/rdm/nexus_rsa_syslog/","--srdm-table","srdm.defender_device_events", "--modified-before", "2023-06-07 12:00:00", "--modified-after", "2023-06-07 11:00:00", "--config-path")
    configs = inputFiles.map { inputFile =>
      val path = getClass.getResource(s"/parserimpl/xml/input/$inputFile").getPath
      val config = XMLParser.getConfig(spark, XMLParser.getParseCmdArgs(params :+ s"file:$path"))
      (inputFile, config)
    }.toMap
    config1 = configs("config1.json")
    config2 = configs("basic_config.json")
    config3 = configs("config2.json")

  }
  after {

  }

  override def conf: SparkConf = super.conf.set("spark.sql.session.timeZone", "UTC")
  val errorColumn: String = "_corrupt_record"


  "it" should "parse xml data with multiple row tags properly" in {

    val df = spark.read.schema(schema).format("json").load(getClass.getResource("/parserimpl/xml/input/input.json").getPath)
    val actualDf = XMLParser.parse(spark, df, config1)
    val expected = spark.read.format("json").load(getClass.getResource("/parserimpl/xml/output/output.json").getPath)
    assertDataFrameDataEquals(actualDf.orderBy("qid"), expected.orderBy("qid"))
  }

  "it" should "parse xml data with single row tag" in {
    val df = spark.read.schema(schema).format("json").load(getClass.getResource("/parserimpl/xml/input/basic.json").getPath)
    val actualDf = XMLParser.parse(spark, df, config2)
    val expectedDf = spark.read.schema(actualDf.schema).format("json").load(getClass.getResource("/parserimpl/xml/output/basic.json").getPath)
    assertDataFrameDataEquals(actualDf,expectedDf)

  }

  "it" should "parse xml data with different data types" in {
    val df = spark.read.schema(schema).format("json").load(getClass.getResource("/parserimpl/xml/input/datatype.json").getPath)
    val actualDf = XMLParser.parse(spark, df, config3)
    val expectedDf = spark.read.schema(actualDf.schema).format("json").load(getClass.getResource("/parserimpl/xml/output/datatype.json").getPath)
    assertDataFrameDataEquals(actualDf.orderBy(expectedDf.columns.map(col): _*),expectedDf.orderBy(expectedDf.columns.map(col): _*))

  }
  "it" should "parse xml data with special characters" in {
    val df = spark.read.schema(schema).format("json").load(getClass.getResource("/parserimpl/xml/input/special_character.json").getPath)
    val actualDf = XMLParser.parse(spark, df, config3)
    val expectedDf = spark.read.format("json").load(getClass.getResource("/parserimpl/xml/output/special_character.json").getPath)
    assertDataFrameDataEquals(actualDf.orderBy(expectedDf.columns.map(col): _*), expectedDf.orderBy(expectedDf.columns.map(col): _*))
  }
  "it" should "not parse corrupt record" in {
    val df = spark.read.schema(schema).format("json").load(getClass.getResource("/parserimpl/xml/input/corrupt_record.json").getPath)
    val actualDf = XMLParser.parse(spark, df, config3)
    assertResult(2)(actualDf.columns.length)
  }

}
