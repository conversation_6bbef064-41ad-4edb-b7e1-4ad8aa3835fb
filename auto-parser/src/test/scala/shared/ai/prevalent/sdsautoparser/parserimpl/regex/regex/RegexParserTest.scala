package shared.ai.prevalent.sdsautoparser.parserimpl.regex.regex

import ai.prevalent.sdsautoparser.parserimpl.regex.regex.RegexParser
import ai.prevalent.sdsautoparser.parserimpl.regex.regex.configs.RegexParserConfig
import ai.prevalent.sdspecore.test.utils.SharedSessionTestTrait
import org.apache.spark.sql.functions.{col, expr}
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec

class RegexParserTest extends AnyFlatSpec with SharedSessionTestTrait with BeforeAndAfter {


  var config1: RegexParserConfig = _
  var config2: RegexParserConfig = _
  var config3: RegexParserConfig = _
  var config4: RegexParserConfig = _
  var df: org.apache.spark.sql.DataFrame = _
  var configs: Map[String, RegexParserConfig] = _
  var configPaths: List[String] = _
  var inputFiles: List[String] = _
  var params: Array[String] = _

  before {
    df = spark.read.json(getClass.getResource("/parserimpl/regex/regex/input/input.json").getPath)
    inputFiles = List("config1.json", "config2.json", "config3.json", "config4.json")
    params = Array("--spark-service", "spark","--rdm-path", "s3a://<%AWS_DATALAKE_BUCKET_NAME%>/ps-migration/sds-autoparser/rdm/pam_cyber_ark/","--srdm-table","<%SRDM_SCHEMA_NAME%>.pam_cyber_ark", "--modified-before", "2023-06-07 12:00:00", "--modified-after", "2023-06-07 11:00:00", "--config-path")
    configs = inputFiles.map { inputFile =>
      val path = getClass.getResource(s"/parserimpl/regex/regex/input/$inputFile").getPath
      val config = RegexParser.getConfig(spark, RegexParser.getParseCmdArgs(params :+ s"file:$path"))
      (inputFile, config)
    }.toMap
    config1 = configs("config1.json")
    config2 = configs("config2.json")
    config3 = configs("config3.json")
    config4 = configs("config4.json")

  }

  "it" should "parse regex data" in {
    val outputDf = RegexParser.parse(spark, df, config1)
    val expectedDF = spark.read.json(getClass.getResource("/parserimpl/regex/regex/output/output.json").getPath)
    assertDataFrameDataEquals(outputDf, expectedDF)
  }
  "it" should "take the expression in global regex pattern if regexpattern is not provided" in {
    val outputDf = RegexParser.parse(spark, df, config2)
    val expectedDF = spark.read.json(getClass.getResource("/parserimpl/regex/regex/output/output.json").getPath)
    assertDataFrameDataEquals(outputDf, expectedDF)

  }
  "it" should "return null if the regexpattern did'nt match" in {
    val outputDf = RegexParser.parse(spark, df, config3)
    assertResult(1)(outputDf.select("cef").filter("cef is null").count())
  }
  "it" should "handle different data types" in {
    //parses string,timestamp,long,boolean
    val df1 = spark.read.json(getClass.getResource("/parserimpl/regex/regex/input/input2.json").getPath)
    val outputDf = RegexParser.parse(spark, df1, config4).drop("payload")
    val expectedDF = spark.read.schema(outputDf.schema).json(getClass.getResource("/parserimpl/regex/regex/output/output2.json").getPath).withColumn("time", expr("to_timestamp(time)")).drop("payload")
    assert(outputDf.orderBy(expectedDF.columns.map(col): _*).collect() === expectedDF.orderBy(expectedDF.columns.map(col): _*).orderBy().collect())


  }
}


