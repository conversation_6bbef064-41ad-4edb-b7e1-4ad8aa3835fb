package shared.ai.prevalent.sdsautoparser.parserimpl.csv

import org.scalatest.flatspec.AnyFlatSpec
import ai.prevalent.sdsautoparser.parserimpl.csv.CSVParser.parse
import ai.prevalent.sdsautoparser.parserimpl.csv.{<PERSON>VParser, CSVParserConfig}
import ai.prevalent.sdspecore.test.utils.SharedSessionTestTrait
import org.apache.spark.sql.functions.col
import org.json4s.MappingException
import org.scalatest.BeforeAndAfter


class CSVParserTest extends AnyFlatSpec with SharedSessionTestTrait with BeforeAndAfter {

  var config1: CSVParserConfig = _
  var config2: CSVParserConfig = _
  var config3: CSVParserConfig = _
  var config4: CSVParserConfig = _
  var config5: CSVParserConfig = _
  var config6: CSVParserConfig = _
  var config7: CSVParserConfig = _
  var config8: CSVParserConfig = _
  var dfPath:String=_

  var configs: Map[String, CSVParserConfig] = _
  var configPaths: List[String] = _
  var inputFiles: List[String] = _
  var params: Array[String] = _
  val filterCondition="_corrupt_record IS NOT NULL"

  before {
    dfPath=getClass.getResource("/parserimpl/csv/input/bmc1.txt").getPath
    inputFiles = List("input.json", "config3.json", "config4.json", "config6.json", "config7.json", "config_comma.json")
    params = Array("--spark-service", "spark", "--rdm-path", "s3a://pai-dev-datalake/sds-autoloader/rdm/nexus_rsa_syslog/","--srdm-table","iceberg_catalog.tel_srdm.srdm_nexus_rsa_syslog","--modified-before", "2023-06-07 12:00:00", "--modified-after", "2023-06-07 11:00:00", "--config-path")
    configs = inputFiles.map { inputFile =>
      val path = getClass.getResource(s"/parserimpl/csv/input/$inputFile").getPath
      val config = CSVParser.getConfig(spark, CSVParser.getParseCmdArgs(params :+ s"file:$path"))
      (inputFile, config)
    }.toMap
    config1 = configs("input.json")
    config3 = configs("config3.json")
    config4 = configs("config4.json")
    config6 = configs("config6.json")
    config7 = configs("config7.json")
    config8 = configs("config_comma.json")

  }


  "It" should "run without error" in {
    val df = spark.read.text(dfPath).withColumnRenamed("value", "payload")
    val outputDf = parse(spark, df, config1)
    assert(outputDf.select("_corrupt_record").filter(filterCondition).count() == 1)
    val expectedDf = spark.read.schema(outputDf.schema).json(getClass.getResource("/parserimpl/csv/output/expected_out.json").getPath)
    val finalOutputDf = outputDf.orderBy("time").drop("_corrupt_record")
    val finalExpectedDf = expectedDf.orderBy("time").drop("_corrupt_record")
    assertDataFrameDataEquals(finalExpectedDf, finalOutputDf)

  }
  "it" should "handle invalid csv configuration" in {
    val path = getClass.getResource(s"/parserimpl/csv/input/config2.json").getPath
    assertThrows[MappingException] {
      CSVParser.getConfig(spark, CSVParser.getParseCmdArgs(params :+ s"file:$path"))
    }

  }
  "it" should "handle when csv length is greater than actual length" in {
    val df = spark.read.text(dfPath).withColumnRenamed("value", "payload")
    val outputDf = parse(spark, df, config3)
    assertResult(4)(outputDf.select("_corrupt_record").filter(filterCondition).count())
  }
  "it" should "handle when invalid csv length is given" in {
    val df = spark.read.text(dfPath).withColumnRenamed("value", "payload")
    val outputDf = parse(spark, df, config4)
    assertResult(4)(outputDf.select("_corrupt_record").filter(filterCondition).count())
  }
  "it" should "parse comma within value" in {
    val df = spark.read.text(getClass.getResource("/parserimpl/csv/input/comma.txt").getPath).withColumnRenamed("value", "payload")
    val outputDf = parse(spark, df, config8)
    val expectedDF = spark.read.schema(outputDf.schema).json(getClass.getResource("/parserimpl/csv/output/comma.json").getPath)
    assertDataFrameDataEquals(outputDf.orderBy(expectedDF.columns.map(col): _*), expectedDF.orderBy(expectedDF.columns.map(col): _*))

  }
  "it" should "parse different data types" in {
    val df = spark.read.text(getClass.getResource("/parserimpl/csv/input/data.txt").getPath).withColumnRenamed("value", "payload")
    val outputDf = parse(spark, df, config7).drop("payload")
    val expectedDF = spark.read.schema(outputDf.schema).json(getClass.getResource("/parserimpl/csv/output/data.json").getPath).drop("payload")
    assertDataFrameDataEquals(outputDf.orderBy(expectedDF.columns.map(col): _*), expectedDF.orderBy(expectedDF.columns.map(col): _*))

  }
  "it" should "parse tab separated data" in {
    val df = spark.read.text(getClass.getResource("/parserimpl/csv/input/bmc.txt").getPath).withColumnRenamed("value", "payload")
    val outputDf = parse(spark, df, config6).drop("payload")
    val expectedDF = spark.read.schema(outputDf.schema).json(getClass.getResource("/parserimpl/csv/output/tab.json").getPath).drop("payload")
    assertResult(0)(outputDf.select("_corrupt_record").filter(filterCondition).count())
    assertDataFrameDataEquals(outputDf.drop("_corrupt_record").orderBy(expectedDF.columns.map(col): _*), expectedDF.drop("_corrupt_record").orderBy(expectedDF.columns.map(col): _*))
  }
  "it" should "not parse when invalid csv format is given" in {
    val df = spark.read.text(getClass.getResource("/parserimpl/csv/input/comma.txt").getPath).withColumnRenamed("value", "payload")
    val outputDf = parse(spark, df, config7)
    assertResult(2)(outputDf.select("_corrupt_record").filter(filterCondition).count())

  }

}

