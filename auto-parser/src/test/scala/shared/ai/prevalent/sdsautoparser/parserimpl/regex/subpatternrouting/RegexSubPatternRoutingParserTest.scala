package shared.ai.prevalent.sdsautoparser.parserimpl.regex.subpatternrouting

import ai.prevalent.sdsautoparser.parserimpl.regex.subpatternrouting.RegexSubPatternRoutingParser
import ai.prevalent.sdsautoparser.parserimpl.regex.subpatternrouting.config.RegexSubPatternRoutingParserConfig
import ai.prevalent.sdspecore.test.utils.SharedSessionTestTrait
import org.apache.spark.sql.types.{DataType, StructType}
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec


class RegexSubPatternRoutingParserTest extends AnyFlatSpec with SharedSessionTestTrait with BeforeAndAfter {

  import spark.implicits._


  var config1: RegexSubPatternRoutingParserConfig = _
  var config2: RegexSubPatternRoutingParserConfig = _
  var dfSql: String = _
  var schema: StructType = _
  var config3: RegexSubPatternRoutingParserConfig = _
  var configs: Map[String, RegexSubPatternRoutingParserConfig] = _
  var configPaths: List[String] = _
  var inputFiles: List[String] = _
  var params: Array[String] = _

  before {
    dfSql = "{\"type\":\"struct\",\"fields\":[{\"name\":\"payload\",\"type\":\"string\",\"nullable\":true}]}"
    schema = DataType.fromJson(dfSql).asInstanceOf[StructType]
    inputFiles = List("config1.json", "config2.json", "config3.json")
    params = Array("--spark-service", "spark","--rdm-path","<%DATALAKE_URI%>/ingestion/rdm/microsoft/windows_security_logs/","--srdm-table","<%SRDM_SCHEMA_NAME%>.microsoft__windows_security_logs", "--modified-before", "2023-06-07 12:00:00", "--modified-after", "2023-06-07 11:00:00", "--config-path")
    configs = inputFiles.map { inputFile =>
      val path = getClass.getResource(s"/parserimpl/regex/subpatternrouting/input/$inputFile").getPath
      val config = RegexSubPatternRoutingParser.getConfig(spark, RegexSubPatternRoutingParser.getParseCmdArgs(params :+ s"file:$path"))
      (inputFile, config)
    }.toMap
    config1 = configs("config1.json")
    config2 = configs("config2.json")
    config3 = configs("config3.json")
  }
  "it" should "parse data a single event code" in {
    val df = spark.read.option("wholetext", "true").text(getClass.getResource("/parserimpl/regex/subpatternrouting/input/4624.txt").getPath).withColumnRenamed("value", "payload")
    val outputDf = RegexSubPatternRoutingParser.applyCustomRegexExtraction(df, config1)
    val expected = spark.read.schema(outputDf.schema).json(getClass.getResource("/parserimpl/regex/subpatternrouting/output/output.json").getPath)
    assertDataFrameDataEquals(expected, outputDf)
  }
  "it" should "parse values in the same key in different event code" in {
    val filesRDD = spark.sparkContext.wholeTextFiles(getClass.getResource("/parserimpl/regex/subpatternrouting/input/event_code/").getPath)
    val filesDF = filesRDD.toDF("file_path", "content")
    val df1 = filesDF.select("content").withColumnRenamed("content", "payload")
    val outputDf1 = RegexSubPatternRoutingParser.applyCustomRegexExtraction(df1, config2)
    val expected = spark.read.schema(outputDf1.schema).json(getClass.getResource("/parserimpl/regex/subpatternrouting/output/output1.json").getPath)
    assertDataFrameDataEquals(outputDf1, expected)
  }
  "it" should "return null if the regex pattern did'nt match" in {
    val df = spark.read.option("wholetext", "true").text(getClass.getResource("/parserimpl/regex/subpatternrouting/input/input.txt").getPath).withColumnRenamed("value", "payload")
    val outputDf = RegexSubPatternRoutingParser.applyCustomRegexExtraction(df, config3)
    val expected = spark.read.schema(outputDf.schema).json(getClass.getResource("/parserimpl/regex/subpatternrouting/output/output3.json").getPath)
    assertDataFrameDataEquals(expected, outputDf)
  }

}
