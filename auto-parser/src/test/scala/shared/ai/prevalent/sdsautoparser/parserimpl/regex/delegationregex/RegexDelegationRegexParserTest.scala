package shared.ai.prevalent.sdsautoparser.parserimpl.regex.delegationregex

import ai.prevalent.sdsautoparser.parserimpl.regex.delegationregex.RegexDelegationRegexParser
import ai.prevalent.sdsautoparser.parserimpl.regex.delegationregex.config.RegexDelegationRegexParserConfig
import ai.prevalent.sdsautoparser.utils.OrderedMapSerializer
import ai.prevalent.sdspecore.test.utils.SharedSessionTestTrait
import ai.prevalent.sdspecore.utils.ConfigUtils
import org.json4s.{DefaultFormats, Formats}
import org.scalatest.flatspec.AnyFlatSpec

class RegexDelegationRegexParserTest extends AnyFlatSpec with SharedSessionTestTrait {
  def configFormats : Formats = DefaultFormats + OrderedMapSerializer
  val jsonConfig: String = """{"rdmPath":"dsp_nexus__syslog","srdmTableName":"srdm.dsp_nexus__syslog","sdmTableName":"sdm.dsp_nexus__syslog","transformationConfig":[{"event_timestamp_epoch":"CAST(to_unix_timestamp(date, 'yyyy-MM-dd') AS LONG)*1000"}],"regexFields":{"appliance_name":{"fieldType":"string","regexGroupIndex":1}},"globalRegexPattern":"(^[^\r\n]++)","delegationRegexFields":{"appliance_name":{"regexGroupIndex":1,"delegationConfig":[{"typeName":"F5-BigIP-APM","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:(?:\\s|[^:\\s]++:)[\\s\\S]++","parserConfigDelegation":{"fields":[{"name":"device_hostname_1","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+(?<bigipDeviceHostname>[\\S]++)\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:(?:\\s|[^:\\s]++:)"},{"name":"session_request_client_ip","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+New session from client IP\\s+(?<bigipClientip>[\\S]++)"},{"name":"user_id","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Logging Agent: VPN session started for user with Internet IP address [^,]*+,\\s+UserID:(?>\\s+(?<bigipUserId>[\\s\\S]+?),\\s*VPN)"},{"name":"state","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+New session from client IP\\s+[\\S]++\\s+\\(ST=(?<state>[^\/]++)\/"},{"name":"country","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+New session from client IP\\s+[\\S]++\\s+\\(ST=[^\/]*+\/CC=(?<country>[^\/]++)\/"},{"name":"continent","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+New session from client IP\\s+[\\S]++\\s+\\(ST=[^\/]*+\/CC=[^\/]*+\/C=(?<continent>[^)]++)\\)"},{"name":"client_user_agent_1","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Logging Agent: VPN session started for user with Internet IP address\\s+[^,]*+,\\s+UserID:(?>\\s+[\\s\\S]*?,\\s+VPN assigned IP address:)(?>\\s+[\\s\\S]*?,\\s+ClientPlatform:)(?>\\s+(?<clientUserAgt>[\\s\\S]+?),\\s+Hostname:)"},{"name":"client_user_agent_2","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Received User-Agent header:\\s+(?<receivedUsrAgnt>[^\r\n]++)"},{"name":"username_1","regexGroupIndex":2,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Username\\s+(\\\\')(?<username>[^\\\\]++)(\\\\')"},{"name":"big_ip_hostname_1","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Logging Agent: VPN session started for user with Internet IP address\\s+[^,]*+,\\s+UserID:(?>\\s+[\\s\\S]*?,\\s+VPN assigned IP address:)(?>\\s+[\\s\\S]*?,\\s+ClientPlatform:)(?>\\s+[\\s\\S]*?,\\s+Hostname:)\\s+(?<bigipHostname>[^,]++),\\s+FQDN:"},{"name":"big_ip_hostname_2","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Updating client info -\\s*(?>Hostname:\\s+(?<updHostname>[\\s\\S]*?)\\s*\bType:)"},{"name":"big_ip_hostname_3","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Received client info -\\s*(?>Hostname:\\s+(?<recHostname>[\\s\\S]*?)\\s*\bType:)"},{"name":"client_type_1","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Received client info -(?>(?>\\s+Hostname:\\s+[\\s\\S]*?\\s+Type:\\s+)|\\s+Type:\\s+)(?>(?<recClientType>[\\s\\S]*?)\\s*\bVersion:)"},{"name":"client_type_2","regexGroupIndex":1,"fieldType":"string","pattern":"(?i)\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Updating client info -\\s+(?>Hostname:\\s+[\\s\\S]*?\\s+Type:)(?>\\s+(?<updClientType>[\\s\\S]*?)\\s*\bVersion:)"},{"name":"client_version_1","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Received client info -(?>(?>\\s+Hostname:\\s+[\\s\\S]*?\\s+Type:\\s+)|\\s+Type:\\s+)(?>[\\s\\S]*?\\s*\bVersion:)(?>\\s+(?<recClientVersion>[\\s\\S]*?)\\s*\bPlatform:\\s+)"},{"name":"client_version_2","regexGroupIndex":1,"fieldType":"string","pattern":"(?i)\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Updating client info -\\s+(?>Hostname:\\s+[\\s\\S]*?\\s+Type:)(?>\\s+[\\s\\S]*?Version:)(?>\\s+(?<upClientVersion>[\\s\\S]*?)\\s+Platform:)"},{"name":"client_platform_1","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Received client info -(?>(?>\\s*\bHostname:\\s+[\\s\\S]*?\\s*\bType:\\s+)|\\s*\bType:\\s+)(?>[\\s\\S]*?\\s*\bVersion:)(?>\\s+[\\s\\S]*?\\s*\bPlatform:)(?>\\s+(?<recClientPlatform>[\\s\\S]*?)\\s*\bCPU:\\s+)"},{"name":"client_platform_2","regexGroupIndex":1,"fieldType":"string","pattern":"(?i)\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Updating client info -\\s+(?>Hostname:\\s+[\\s\\S]*?\\s*\bType:)(?>\\s+[\\s\\S]*?\\s*\bVersion:)(?>\\s+[\\s\\S]*?\\s*\bPlatform:)(?>\\s+(?<updClientPlatform>[\\s\\S]*?)\\s*\bCPU:\\s+)"},{"name":"client_model","regexGroupIndex":2,"fieldType":"string","pattern":"(?i)\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Updating client info -\\s+(?>Hostname:\\s+[\\s\\S]*?\\s+Type:)(?>\\s+[\\s\\S]*?Version:)(?>\\s+[\\s\\S]*?Platform:)(?>\\s+(?<updClientPlatform>[\\s\\S]*?)\\s+Model:)(?>\\s+(?<clientModel>[\\s\\S]*?)\\s+Platform Version:\\s+)"},{"name":"client_platform_version","regexGroupIndex":2,"fieldType":"string","pattern":"(?i)\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Updating client info -\\s+(?>Hostname:\\s+[\\s\\S]*?\\s+Type:)(?>\\s+[\\s\\S]*?Version:)(?>\\s+[\\s\\S]*?Platform:)(?>\\s+(?<updClientPlatform>[\\s\\S]*?)\\s+Model:)(?>\\s+[\\s\\S]*?\\s+Platform Version:)(?>\\s+(?<clientPlatformVersion>[\\s\\S]*?)\\s+Serial Number:)"},{"name":"session_connect_client_ip","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Assigned PPP Dynamic IPv4:\\s+[\\S]*+\\s+ID:\\s+[\\S]*+\\s+Tunnel Type:\\s+[\\S]*+\\s+NA Resource:\\s+\\S++\\s+Client IP:\\s+(?<sessionConnectClientip>\\S*+)"},{"name":"device_status","regexGroupIndex":2,"fieldType":"string","pattern":"(?i)\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+mdm\\s+\\[[^\\]]++\\]:\\s+(device id\\s+\\[[^\\]]++\\]:\\s+Device status:\\s+|(?=Device ID was not)|device id \\[[^\\]]++\\]:\\s+(?=Device not found))(?<deviceStatus>[^\r\n]++)"},{"name":"mdm_connector","regexGroupIndex":1,"fieldType":"string","pattern":"(?i)\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+mdm\\s+\\[\/[^\/]++\/(?<mdmConnector>[^\\]]++)"},{"name":"fqdn","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Logging Agent: VPN session started for user with Internet IP address\\s+[^,]*+,\\s+(?>UserID:\\s+[\\s\\S]*?,\\s+VPN assigned IP address:)(?>\\s+[\\s\\S]*?,\\s+ClientPlatform:)(?>\\s+[\\s\\S]*?,\\s+Hostname:)\\s+[^,]*+,\\s+FQDN:\\s+(?<fqdn>[\\S]++)\\s+and"},{"name":"uri","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Logging Agent: VPN session started for user with Internet IP address\\s+[^,]*+,\\s+(?>UserID:\\s+[\\s\\S]*?,\\s+VPN assigned IP address:)(?>\\s+[\\s\\S]*?,\\s+ClientPlatform:)(?>\\s+[\\s\\S]*?,\\s+Hostname:)\\s+[^,]*+,\\s+FQDN:\\s+[\\S]*+\\s+and\\s+URI:\\s+(?<uri>[\\S]++)"},{"name":"access_policy_result","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Access policy result:\\s+(?<accessPolicyResult>[\\S]++)"},{"name":"access_profile","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:\\s+(?<accessProfile>[^:\\s]++):[^:\\s]++:[a-f0-9]++:(?:\\s|[^:\\s]++:)"},{"name":"access_policy_from","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Following rule\\s+\\\\'[^\\\\]++\\\\'\\s+from\\s+[\\S]++\\s+\\\\'(?<accessPolicyFrom>[^\\\\]++)\\\\'\\s+to\\s[\\S]++"},{"name":"access_policy_to","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Following rule\\s+\\\\'[^\\\\]++\\\\'\\s+from\\s+[\\S]++\\s+\\\\'[^\\\\]++\\\\'\\s+to\\s[\\S]++\\s+\\\\'(?<accessPolicyTo>[^\\\\]++)\\\\'"},{"name":"access_policy_branch","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Following rule\\s+\\\\'(?<accessPolicyBranch>[^\\\\]++)\\\\'\\s+from"},{"name":"session_variable","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Session variable\\s+\\\\'(?<sessionVariable>[^\\\\]++)\\\\'\\s+set"},{"name":"session_variable_value","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Session variable\\s+\\\\'[^\\\\]++\\\\'\\s+set\\s+to\\s+\\\\'(?<sessionVariableValue>[^\\\\]++)\\\\'"},{"name":"connected_ip","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Assigned PPP Dynamic IPv4:\\s+(?<bigipConnectedIp>[\\S]++)"},{"name":"tunnel_type","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Assigned PPP Dynamic IPv4:\\s+[\\S]*+\\s+ID:\\s+[\\S]*+\\s+Tunnel Type:\\s+(?<tunnelType>[\\S]++)\\s+NA Resource:"},{"name":"na_resource","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Assigned PPP Dynamic IPv4:\\s+[\\S]*+\\s+ID:\\s+[\\S]*+\\s+Tunnel Type:\\s+[\\S]*+\\s+NA Resource:\\s+(?<naResource>[\\S]++)\\s+Client IP:"},{"name":"session_id","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)(?<sessionId>[a-f0-9]++):(?:\\s|[^:\\s]++:)"},{"name":"bytes_in","regexGroupIndex":1,"fieldType":"long","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Session statistics -\\s+bytes in:\\s+(?<bytesIn>[^,]++),\\s+bytes out:"},{"name":"bytes_out","regexGroupIndex":1,"fieldType":"long","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Session statistics -\\s+bytes in:\\s+[^,]*+,\\s+bytes out:\\s+(?<bytesOut>[\\S]++)"},{"name":"event_utc_log_time","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>(?<eventUtcLogTime>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2})"},{"name":"tunnel_id_1","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+PPP tunnel 0x[a-fA-F0-9]++ \\(ID: (?<tunnelID>[0-9a-fA-F]++)\\)"},{"name":"tunnel_id_2","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Assigned PPP Dynamic IPv4:\\s+[\\S]*+\\s+ID:\\s+(?<tunnelID>[0-9a-fA-F]*+)\\s+Tunnel Type:"},{"name":"session_end_reason_1","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Session deleted \\((?<sessionEndReason>[^\\)]++)"},{"name":"session_end_reason_2","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:\\s+Session deleted due to (?<sessionEndReason>[^\r\n]+?)(\\.|$)"},{"name":"event_id","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+(?<eventID>[0-9a-fA-F]++):[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:(?:\\s|[^:\\s]++:)"},{"name":"av_sdk","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:[^:\\s]++: endpoint software detected \\(sdk=(?<avSDK>[^\r\n]*?),fieldType=av"},{"name":"av_version","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:[^:\\s]++: endpoint software detected \\(sdk=[^\r\n]*?,fieldType=av,id=[^\r\n]*?,vendor_id=[^\r\n]*?,version=(?<avVersion>[^\r\n]*?),state="},{"name":"av_name","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:[^:\\s]++: endpoint software detected \\(sdk=[^\r\n]*?,fieldType=av,id=[^\r\n]*?,vendor_id=[^\r\n]*?,version=[^\r\n]*?,state=[^\r\n]*?,name=(?<avName>[^\r\n]*?),vendor_name="},{"name":"av_vendor_name","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:[^:\\s]++: endpoint software detected \\(sdk=[^\r\n]*?,fieldType=av,id=[^\r\n]*?,vendor_id=[^\r\n]*?,version=[^\r\n]*?,state=[^\r\n]*?,name=[^\r\n]*?,vendor_name=(?<avVendorName>[^\r\n]*?),db_signature="},{"name":"av_missing_updates","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:[^:\\s]++: endpoint software detected \\(sdk=[^\r\n]*?,fieldType=av,id=[^\r\n]*?,vendor_id=[^\r\n]*?,version=[^\r\n]*?,state=[^\r\n]*?,name=[^\r\n]*?,vendor_name=[^\r\n]*?,missing_updates=(?<avMissingUpdates>[^\r\n]*?)\\)"},{"name":"fw_sdk","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:[^:\\s]++: endpoint software detected \\(sdk=(?<fwSDK>[^\r\n]*?),fieldType=fw,"},{"name":"fw_version","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:[^:\\s]++: endpoint software detected \\(sdk=[^\r\n]*?,fieldType=fw,[^\r\n]*?version=(?<fwVersion>[^\r\n]*?),"},{"name":"fw_name","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:[^:\\s]++: endpoint software detected \\(sdk=[^\r\n]*?,fieldType=fw,[^\r\n]*?,name=(?<fwName>[^\r\n]*?),vendor_name="},{"name":"fw_vendor_name","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:[^:\\s]++: endpoint software detected \\(sdk=[^\r\n]*?,fieldType=fw,[^\r\n]*?,vendor_name=(?<fwVendorName>[^\r\n]*?),db_signature"},{"name":"fw_missing_updates","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+[\\S]++\\s+[^\\[\\s]++\\[[0-9a-fA-F]++\\]:\\s+[0-9a-fA-F]++:[0-9a-fA-F]:(?>\\s+[^:\\s]++:[^:\\s]++:|\\s+)[a-f0-9]++:[^:\\s]++: endpoint software detected \\(sdk=[^\r\n]*?,fieldType=fw,id=[^\r\n]*?,vendor_id=[^\r\n]*?,version=[^\r\n]*?,state=[^\r\n]*?,name=[^\r\n]*?,vendor_name=[^\r\n]*?,missing_updates=(?<fwMissingUpdates>[^\r\n]*?)\\)"}]},"fieldTransformations":[{"transformation":"sparkSQL","config":[{"device_hostname":"regexp_extract(device_hostname_1,'\\\\A([^.]++)[\\\\S]*',1)","hostname":"CASE WHEN(big_ip_hostname_1 IS NOT NULL) THEN big_ip_hostname_1 WHEN(big_ip_hostname_2 IS NOT NULL) THEN big_ip_hostname_2 WHEN(big_ip_hostname_3 IS NOT NULL) THEN big_ip_hostname_3 END","username":"TRIM(username_1)"}]}]},{"typeName":"Juniper","pattern":"\\A<\\d+>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}\\s+[\\S]++\\s+Juniper:[\\S\\s]++","parserConfigDelegation":{"fields":[{"name":"device_hostname","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d++>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}[ \t]++\\S++[ \t]++Juniper:[ \t]++\\d{4}-\\d{2}-\\d{2}[ \t]++\\d{2}:\\d{2}:\\d{2}[ \t]++\\S++[ \t]++(?<deviceHostname>\\S++)"},{"name":"ivs","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d++>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}[ \t]++\\S++[ \t]++Juniper:[ \t]++\\d{4}-\\d{2}-\\d{2}[ \t]++\\d{2}:\\d{2}:\\d{2}[ \t]++\\S++[ \t]++\\S++[ \t]++\\S++[ \t]++\\[[^]]++\\][ \t]++(?<ivs>[^:]++)::"},{"name":"realm","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d++>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}[ \t]++\\S++[ \t]++Juniper:[ \t]++\\d{4}-\\d{2}-\\d{2}[ \t]++\\d{2}:\\d{2}:\\d{2}[ \t]++\\S++[ \t]++\\S++[ \t]++\\S++[ \t]++\\[[^]]++\\][ \t]++[^:]++::[^(]*+\\((?<realm>[^)]*+)\\)\\["},{"name":"role","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d++>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}[ \t]++\\S++[ \t]++Juniper:[ \t]++\\d{4}-\\d{2}-\\d{2}[ \t]++\\d{2}:\\d{2}:\\d{2}[ \t]++\\S++[ \t]++\\S++[ \t]++\\S++[ \t]++\\[[^]]++\\][ \t]++[^:]++::[^(]*+\\([^)]*+\\)\\[(?<role>[^\\]]*+)\\][ \t]++"},{"name":"client_ip","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d++>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}[ \t]++\\S++[ \t]++Juniper:[ \t]++\\d{4}-\\d{2}-\\d{2}[ \t]++\\d{2}:\\d{2}:\\d{2}[ \t]++\\S++[ \t]++\\S++[ \t]++\\S++[ \t]++\\[(?<clientIP>[^]]++)\\][ \t]++[^:]++::"},{"name":"user_id","regexGroupIndex":2,"fieldType":"string","pattern":"\\A<\\d++>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}[ \t]++\\S++[ \t]++Juniper:[ \t]++\\d{4}-\\d{2}-\\d{2}[ \t]++\\d{2}:\\d{2}:\\d{2}[ \t]++\\S++[ \t]++\\S++[ \t]++\\S++[ \t]++\\[(?<clientIP>[^]]++)\\][ \t]++[^:]++::(?<userID>[^\\(]*+)\\("},{"name":"client_hostname","regexGroupIndex":2,"fieldType":"string","pattern":"\\A<\\d++>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}[ \t]++\\S++[ \t]++Juniper:[ \t]++\\d{4}-\\d{2}-\\d{2}[ \t]++\\d{2}:\\d{2}:\\d{2}[ \t]++\\S++[ \t]++\\S++[ \t]++\\S++[ \t]++\\[(?<clientIP>[^]]++)\\][ \t]++[^:]++::[^\\(]*+\\([^\\)]*+\\)\\[[^\\]]*+\\][ -]++VPN Tunneling:[^\r\n]*?, hostname (?<clientHostname>\\S++)"},{"name":"changed_client_ip","regexGroupIndex":4,"fieldType":"string","pattern":"\\A<\\d++>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}[ \t]++\\S++[ \t]++Juniper:[ \t]++\\d{4}-\\d{2}-\\d{2}[ \t]++\\d{2}:\\d{2}:\\d{2}[ \t]++\\S++[ \t]++\\S++[ \t]++\\S++[ \t]++\\[([^]]++)\\][ \t]++[^:]++::([^\\(]*+)\\(([^\\)]*+)\\)\\[[^\\]]*+\\][ -]++Remote address for user \\2\/\\3 changed from \\1 to (?<changedClientIP>(?:(?>25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-8][0-9]|[0-9])\\.){3}(?>25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-8][0-9]|[0-9]))\\. Access denied\\."},{"name":"policy_name_1","regexGroupIndex":4,"fieldType":"string","pattern":"(?i)\\A<\\d++>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}[ \t]++\\S++[ \t]++Juniper:[ \t]++\\d{4}-\\d{2}-\\d{2}[ \t]++\\d{2}:\\d{2}:\\d{2}[ \t]++\\S++[ \t]++\\S++[ \t]++\\S++[ \t]++\\[([^]]++)\\][ \t]++[^:]++::([^\\(]*+)\\(([^\\)]*+)\\)\\[[^\\]]*+\\][ -]++Host Checker policy \\\\'(?<policyName>[^\\\\]++)\\\\'"},{"name":"policy_verdict","regexGroupIndex":4,"fieldType":"string","pattern":"(?i)\\A<\\d++>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}[ \t]++\\S++[ \t]++Juniper:[ \t]++\\d{4}-\\d{2}-\\d{2}[ \t]++\\d{2}:\\d{2}:\\d{2}[ \t]++\\S++[ \t]++\\S++[ \t]++\\S++[ \t]++\\[([^]]++)\\][ \t]++[^:]++::([^\\(]*+)\\(([^\\)]*+)\\)\\[[^\\]]*+\\][ -]++Host Checker policy \\\\'[^\\\\]++\\\\' (?<policyVerdict>\\S++)[ \t]++"},{"name":"policy_failure_reason","regexGroupIndex":4,"fieldType":"string","pattern":"(?i)\\A<\\d++>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}[ \t]++\\S++[ \t]++Juniper:[ \t]++\\d{4}-\\d{2}-\\d{2}[ \t]++\\d{2}:\\d{2}:\\d{2}[ \t]++\\S++[ \t]++\\S++[ \t]++\\S++[ \t]++\\[([^]]++)\\][ \t]++[^:]++::([^\\(]*+)\\(([^\\)]*+)\\)\\[[^\\]]*+\\][ -]++Host Checker policy \\\\'[^\\\\]++\\\\' \\S++[ \t]++[^\r\n]*?Reason: \\\\'(?<policyFailReason>[^\r\n]+?)\\\\'"},{"name":"primary_auth_status","regexGroupIndex":4,"fieldType":"string","pattern":"(?i)\\A<\\d++>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}[ \t]++\\S++[ \t]++Juniper:[ \t]++\\d{4}-\\d{2}-\\d{2}[ \t]++\\d{2}:\\d{2}:\\d{2}[ \t]++\\S++[ \t]++\\S++[ \t]++\\S++[ \t]++\\[([^]]++)\\][ \t]++[^:]++::([^\\(]*+)\\(([^\\)]*+)\\)\\[[^\\]]*+\\][ -]++Primary authentication (?<primaryAuthStatus>\\S++) for"},{"name":"connected_ip","regexGroupIndex":4,"fieldType":"string","pattern":"(?i)\\A<\\d++>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}[ \t]++\\S++[ \t]++Juniper:[ \t]++\\d{4}-\\d{2}-\\d{2}[ \t]++\\d{2}:\\d{2}:\\d{2}[ \t]++\\S++[ \t]++\\S++[ \t]++\\S++[ \t]++\\[([^]]++)\\][ \t]++[^:]++::([^\\(]*+)\\(([^\\)]*+)\\)\\[[^\\]]*+\\][ -]++VPN Tunneling: Session (?:started|ended) for user with IPv4 address (?<connectedIP>(?:(?>25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-8][0-9]|[0-9])\\.){3}(?>25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-8][0-9]|[0-9]))"},{"name":"tunnel_duration","regexGroupIndex":4,"fieldType":"long","pattern":"(?i)\\A<\\d++>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}[ \t]++\\S++[ \t]++Juniper:[ \t]++\\d{4}-\\d{2}-\\d{2}[ \t]++\\d{2}:\\d{2}:\\d{2}[ \t]++\\S++[ \t]++\\S++[ \t]++\\S++[ \t]++\\[([^]]++)\\][ \t]++[^:]++::([^\\(]*+)\\(([^\\)]*+)\\)\\[[^\\]]*+\\][ -]++Closed connection to TUN-VPN [^\r\n]*?after (?<tunnelDuration>\\d++)"},{"name":"bytes_read","regexGroupIndex":4,"fieldType":"long","pattern":"(?i)\\A<\\d++>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}[ \t]++\\S++[ \t]++Juniper:[ \t]++\\d{4}-\\d{2}-\\d{2}[ \t]++\\d{2}:\\d{2}:\\d{2}[ \t]++\\S++[ \t]++\\S++[ \t]++\\S++[ \t]++\\[([^]]++)\\][ \t]++[^:]++::([^\\(]*+)\\(([^\\)]*+)\\)\\[[^\\]]*+\\][ -]++Closed connection to TUN-VPN [^\r\n]*?(?<bytesRead>\\d++) bytes read"},{"name":"bytes_written","regexGroupIndex":4,"fieldType":"long","pattern":"(?i)\\A<\\d++>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}[ \t]++\\S++[ \t]++Juniper:[ \t]++\\d{4}-\\d{2}-\\d{2}[ \t]++\\d{2}:\\d{2}:\\d{2}[ \t]++\\S++[ \t]++\\S++[ \t]++\\S++[ \t]++\\[([^]]++)\\][ \t]++[^:]++::([^\\(]*+)\\(([^\\)]*+)\\)\\[[^\\]]*+\\][ -]++Closed connection to TUN-VPN [^\r\n]*?(?<bytesWritten>\\d++) bytes written"},{"name":"event_utc_log_time","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d++>(?<eventUTCLogTtime>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2})[ \t]++\\S++[ \t]++Juniper:[ \t]++\\d{4}-\\d{2}-\\d{2}[ \t]++\\d{2}:\\d{2}:\\d{2}"},{"name":"event_local_log_time","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d++>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2}[ \t]++\\S++[ \t]++Juniper:[ \t]++(?<eventLocalLogTime>\\d{4}-\\d{2}-\\d{2}[ \t]++\\d{2}:\\d{2}:\\d{2})"}]},"fieldTransformations":[{"transformation":"sparkSQL","config":[{"policy_name":"cast ('Check transformations' as string)"}]}]},{"typeName":"syslogNoise","pattern":"[\\s\\S]","parserConfigDelegation":{"fields":[{"name":"event_utc_log_time","regexGroupIndex":1,"fieldType":"string","pattern":"\\A<\\d++>(?<eventUTCLogTtime>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}[+-]\\d{2}:\\d{2})[ \t]++\\S++"}]}}]}},"dropColumns":["big_ip_device_hostname","client_user_agent_value","juniper_device_hostname","big_ip_client_ip","juniper_client_ip","big_ip_user_id","juniper_user_id","big_ip_hostname_1","big_ip_hostname_2","big_ip_hostname_3","juniper_hostname","big_ip_connected_ip","juniper_connected_ip","client_user_agent_1","client_user_agent_2","client_type_1","client_type_2","client_version_1","client_version_2","client_platform_1","client_platform_2","time_info_event_timestamp","device_hostname_1","tunnel_id_1","tunnel_id_2","session_end_reason_1","session_end_reason_2","time_offset_map","device_hostname_1","username_1","policy_name_1"]}
                     |""".stripMargin
  val config: RegexDelegationRegexParserConfig = ConfigUtils.getConfigFromJSON[RegexDelegationRegexParserConfig](jsonConfig,formats = configFormats)

  "It" should "run without error" in {
    val df = spark.read.text("src/test/resources/parser/regex/delegationregex/winevent/nexus_syslog.txt").withColumnRenamed("value","payload")
    val output = RegexDelegationRegexParser.applyCustomRegexExtraction(df,config)
    assertResult("Check transformations")(output.select("policy_name").head.get(0))
    assertResult(true)(output.columns.contains("fw_version")) // inside delegationConfig parserConfigDelegation fields - F5-BigIP-APM
    assertResult(true)(output.columns.contains("user_id")) // inside delegationConfig parserConfigDelegation fields - Juniper
    assertResult(true)(output.columns.contains("F5-BigIP-APM")) //inside delegationConfig typeName
    assertResult(true)(output.columns.contains("syslogNoise")) //inside delegationConfig typeName
    assertResult(true)(output.columns.contains("event_utc_log_time")) //inside delegationConfig parserConfigDelegation fields - syslogNoise
  }
}

