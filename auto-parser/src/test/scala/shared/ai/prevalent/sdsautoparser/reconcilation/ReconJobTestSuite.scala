package shared.sdsautoparser.reconcilation

//package ai.prevalent.sdsautoparser.reconcilation
//
//import ai.prevalent.sdsautoparser.utils.ReconJobUtil
//import com.holdenkarau.spark.testing._
//import org.apache.spark.sql.types.StructType
//import org.apache.spark.sql.Row
//import org.scalatest.funsuite.AnyFunSuite
//class ReconJobTestSuite extends AnyFunSuite with DataFrameSuiteBase{
//
//  val T=ReconJobUtil
//
//  test("getCountDF"){
//
//    val df=spark.read.parquet(getClass.getResource("/reconcilation/recon_input").getPath)
//    val c=T.getCount(df)
//    println(c)
//    assert(c==10)
//  }
//
//  test("createDataFrame"){
//    val schema=T.getTableSchema()
//    println(schema)
//    val count:Long=1234
//    val data=Seq(Row(count,"start_time","end_time","created_time","data_source_name","data_model_type"))
//    println(data)
//    val df=T.createOutputDF(spark,data,schema)
//    df.show()
//  }
//}
