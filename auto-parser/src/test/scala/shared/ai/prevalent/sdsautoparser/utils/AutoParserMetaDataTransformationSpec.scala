package shared.ai.prevalent.sdsautoparser.utils

import ai.prevalent.sdsautoparser.parserimpl.json.{JSONParserConfig, MetadataTransformationField}
import ai.prevalent.sdsautoparser.utils.MetadataTransformationUtils.applyMetaDataTransformation
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
import ai.prevalent.sdspecore.sparkbase.table.iceberg.SDSIcebergConnect
import ai.prevalent.sdspecore.test.utils.SharedSessionTestTrait
import ai.prevalent.sdspecore.utils.ConfigUtils
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec
import org.apache.spark.sql.types._
import org.apache.spark.sql.functions.expr
import org.json4s._
import org.json4s.jackson.JsonMethods._
import shared.sdsautoparser.testutils.ColumnChangeConfig



class AutoParserMetaDataTransformationSpec extends AnyFlatSpec with SharedSessionTestTrait with BeforeAndAfter {

  var configString: String = _
  var writer: SDSTableWriter = _
  var reader: SDSTableReader = _

  implicit val formats: DefaultFormats = DefaultFormats
  before {
    configString = """{"metadataTransformationConfig": [{"sourceField": "identity", "childFieldsToMove": ["userAssignedIdentities"], "newColumnName": "identity2"}]}"""
    writer = SDSTableWriterFactory.get(SDSIcebergConnect.name, spark)
    reader = SDSTableReaderFactory.get(SDSIcebergConnect.name, spark)
  }

  "JsonParserConfig" should "parse metadata transformation field with transformation configs" in {
    val config = ConfigUtils.getConfigFromJSON[JSONParserConfig]("""{"transformationConfig":[{"event_timestamp_epoch":"ingested_timestamp"}], "metadataTransformationConfig": [{"sourceField": "identity", "childFieldsToMove": ["userAssignedIdentities"]}]}}""")
    assertTrue(config.metadataTransformationConfig.isInstanceOf[Option[Array[MetadataTransformationField]]])
    assertTrue(config.metadataTransformationConfig.isDefined)
    assertTrue(config.transformationConfig.isDefined)
  }

  "JsonParserConfig" should "parse metadata transformation Field correctly" in {
    val config = parse(configString).extract[ColumnChangeConfig]
    assertTrue(config.metadataTransformationConfig.isInstanceOf[Array[MetadataTransformationField]])
  }

  "applyMetaDataTransformation" should "move metadata fields to value in the dataframe" in {
    val expectedSchema = StructType(Seq(
      StructField("id", LongType, nullable = false),
      StructField("identity", StructType(Seq(
        StructField("principal", StringType, nullable = false),
        StructField("tenantId", LongType, nullable = false),
        StructField("userAssignedIdentities_updated", ArrayType(
          MapType(keyType = StringType, valueType = StringType, valueContainsNull = true),
          containsNull = true), nullable = true)))
      )
    ))
    val sourcePath = getClass.getResource("/utils/MetadataTransformation/input/sample.json").getPath
    val df = spark.read.json(sourcePath)
    val config = parse(configString).extract[ColumnChangeConfig]
    val result = applyMetaDataTransformation(df, config.metadataTransformationConfig)
    assert(result.schema.sql, expectedSchema.sql)
  }

  "applyMetaDataTransformation" should "move metadata fields to value in the dataframe with nested json" in {
    val config = parse(configString).extract[ColumnChangeConfig]
    val df = spark.read.json(getClass.getResource("/utils/MetadataTransformation/input/sample2.json").getPath)
    val transformedDf = applyMetaDataTransformation(df, config.metadataTransformationConfig)
    val result = transformedDf.filter("id=1")
      .select(expr("filter(identity.userAssignedIdentities_updated, x-> x.struct_key == 'subscriptionName2')").as("temp"))
      .select(expr("transform(temp, x->x.snfield2)").as("temp")).collect()
      .map(item => item.mkString(","))
    val expected = Array("ArraySeq({\"nestedField1\":\"sample1\",\"nestedField2\":123})")
    assert(result, expected)
  }
}