package sdsautoparser.utils

import ai.prevalent.sdsautoparser.parserimpl.json.JSONParserConfig
import ai.prevalent.sdsautoparser.utils.OrderedMapSerializer
import ai.prevalent.sdsautoparser.utils.ReconJobUtil.DataLakeReconTableSchema
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableWriter}
import ai.prevalent.sdspecore.utils.ConfigUtils
import org.json4s.{DefaultFormats, Formats}
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec

import java.sql.Connection

class ReconJobUtilSpec extends AnyFlatSpec with BeforeAndAfter {
  var writer: SDSTableWriter = _
  var reader: SDSTableReader = _
  var tableName: String = _
  var df2sql: String = _
  var config: ai.prevalent.sdsautoparser.parserimpl.json.JSONParserConfig = _
  var mockConnection: Connection = _
  def configFormats : Formats = DefaultFormats + OrderedMapSerializer

  before {
    tableName = "srdm.table2"
    df2sql = "select '2022-01-01T01:00:00Z' as parsed_interval_timestamp, 22 as b, struct(1,2) as c,struct(1,2) as cc"
    config = ConfigUtils.getConfigFromJSON[JSONParserConfig]("""{"rdmPath":"s3a:\/\/bucket\/qa\/sds-autoparser\/rdm\/business_unit\/","srdmTableName":"srdm.business_unit","sdmTableName":"ps_sdm_spark.business_unit","transformationConfig":{"event_timestamp_epoch":"ingested_timestamp"}}""",formats = configFormats)

  }

  "DataLakeReconTableSchema" should "contain all expected fields in order" in {
    val schema = DataLakeReconTableSchema
    val expectedFields = Seq(
      "record_count",
      "error_record_count",
      "index_time_start_date",
      "index_time_end_date",
      "created_date",
      "data_model_type",
      "data_source_name"
    )

    assert(schema.map(_.name) == expectedFields)
    assert(schema.map(_.name).length == expectedFields.size)
  }

}
