package shared.ai.prevalent.sdsautoparser.autoparserbase

import ai.prevalent.sdsautoparser.autoparserbase.{AutoParserBase, AutoParserJobArgs}
import ai.prevalent.sdsautoparser.parserimpl.json.JSONParserConfig
import ai.prevalent.sdsautoparser.utils.OrderedMapSerializer
import ai.prevalent.sdspecore.sparkbase.table.iceberg.SDSIcebergConnect
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
import ai.prevalent.sdspecore.test.utils.IcebergSparkTestWrapper
import ai.prevalent.sdspecore.utils.ConfigUtils
import org.apache.spark.SparkConf
import org.apache.spark.sql.functions.{col, expr, to_timestamp}
import org.apache.spark.sql.{Column, DataFrame, SparkSession}
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec
import org.apache.spark.sql.catalyst.expressions.objects.AssertNotNull
import org.json4s.{DefaultFormats, Formats}

import java.io.File
import scala.reflect.io.Directory

class AutoParserBaseTest extends AnyFlatSpec with IcebergSparkTestWrapper with BeforeAndAfter {


  var writer: SDSTableWriter = _
  var reader: SDSTableReader = _
  def configFormats : Formats = DefaultFormats + OrderedMapSerializer
  before {
    spark.sparkContext.setLogLevel("ERROR")
    writer = SDSTableWriterFactory.get(SDSIcebergConnect.name, spark)
    reader = SDSTableReaderFactory.get(SDSIcebergConnect.name, spark)
  }

  after {
    new Directory(new File(warehousePath)).deleteRecursively()
  }


  override def warehousePath: String = {
    new Directory(new File(s"${getClass.getResource("/").getPath}/iceberg_warehouse")).createDirectory()
    getClass.getResource("/iceberg_warehouse").getPath
  }

  override def conf: SparkConf = super.conf.set("spark.sql.iceberg.merge-schema", "true")
    .set("spark.sql.iceberg.set-all-nullable-field", "true")
    .set("spark.sql.iceberg.check-ordering", "false")


  "It" should "update table with provided schemaHint" in {
    val tableName = "testSchema.table"
    val df1Schema = """{"type":"struct","fields":[{"name":"a","type":"integer","nullable":false,"metadata":{}},{"name":"b","type":"integer","nullable":false,"metadata":{}},{"name":"c","type":{"type":"struct","fields":[{"name":"col1","type":"integer","nullable":false,"metadata":{}},{"name":"col2","type":"integer","nullable":false,"metadata":{}}]},"nullable":false,"metadata":{}},{"name":"cc","type":{"type":"struct","fields":[{"name":"col1","type":"integer","nullable":false,"metadata":{}},{"name":"col2","type":"integer","nullable":false,"metadata":{}}]},"nullable":false,"metadata":{}}]}"""
    val config = JSONParserConfig(transformationConfig = None, dropColumns = None,
      schemaHint = Some(df1Schema), granularity = Some("days"),replaceUUIDColumnNameInData = None,
      customJsonSchema = None, inferStructSchema = None, metadataTransformationConfig = None)

    AutoParserImpl.spark = spark
    AutoParserImpl.schemaChangeBasedOnHint(config.schemaHint.get, tableName, config.granularity.get)
    val table1 = reader.read(tableName)
    assertResult(0)(table1.count())

    // with data and new column
    val df2sql = "select 11 as a, 22 as b, struct(1,2) as c,struct(1,2) as cc,True as isValid,to_timestamp('2038-01-19 03:14:07.499999') as event_timestamp_ts,to_timestamp('2038-01-19 03:14:07.499999') as parsed_interval_timestamp_ts"
    val df2 = AutoParserImpl.spark.sql(df2sql)
    reader.read(tableName)
    writer.overwritePartition(df2, tableName)
    AutoParserImpl.schemaChangeBasedOnHint(config.schemaHint.get, tableName, config.granularity.get)
    val table2 = reader.read(tableName)
    assertResult(1)(table2.count())

    val df3Schema =
      """{"type":"struct","fields":[{"name":"a","type":"integer","nullable":false,"metadata":{}},{"name":"b","type":"integer","nullable":false,"metadata":{}},{"name":"c","type":{"type":"struct","fields":[{"name":"col1","type":"integer","nullable":false,"metadata":{}},{"name":"col2","type":"integer","nullable":false,"metadata":{}},{"name":"col3","type":"integer","nullable":false,"metadata":{}}]},"nullable":false,"metadata":{}},{"name":"cc","type":{"type":"struct","fields":[{"name":"col1","type":"integer","nullable":false,"metadata":{}},{"name":"col2","type":"integer","nullable":false,"metadata":{}},{"name":"col3","type":{"type":"struct","fields":[{"name":"col1","type":"integer","nullable":false,"metadata":{}},{"name":"col2","type":"integer","nullable":false,"metadata":{}},{"name":"col3","type":"integer","nullable":false,"metadata":{}}]},"nullable":false,"metadata":{}},{"name":"col4","type":"integer","nullable":false,"metadata":{}}]},"nullable":false,"metadata":{}},{"name":"ccc","type":{"type":"struct","fields":[{"name":"col1","type":"integer","nullable":false,"metadata":{}},{"name":"col2","type":"integer","nullable":false,"metadata":{}},{"name":"col3","type":{"type":"struct","fields":[{"name":"col1","type":"integer","nullable":false,"metadata":{}},{"name":"col2","type":"integer","nullable":false,"metadata":{}},{"name":"col3","type":{"type":"array","elementType":{"type":"struct","fields":[{"name":"col1","type":"integer","nullable":false,"metadata":{}},{"name":"col2","type":"integer","nullable":false,"metadata":{}},{"name":"col3","type":"integer","nullable":false,"metadata":{}}]},"containsNull":false},"nullable":false,"metadata":{}}]},"nullable":false,"metadata":{}},{"name":"col4","type":"integer","nullable":false,"metadata":{}}]},"nullable":false,"metadata":{}},{"name":"d","type":"integer","nullable":false,"metadata":{}},{"name":"cccc","type":{"type":"array","elementType":{"type":"struct","fields":[{"name":"col1","type":"integer","nullable":false,"metadata":{}},{"name":"col2","type":"integer","nullable":false,"metadata":{}}]},"containsNull":false},"nullable":false,"metadata":{}}]}\"\"\"
        |""".stripMargin
    val config3 = JSONParserConfig(transformationConfig = None, dropColumns = None, schemaHint = Some(df3Schema), granularity = Some("days"),
      replaceUUIDColumnNameInData = None, customJsonSchema = None, inferStructSchema = None, metadataTransformationConfig = None)
    AutoParserImpl.schemaChangeBasedOnHint(config3.schemaHint.get, tableName, config3.granularity.get)
    val table3 = reader.read(tableName)
    assertResult(true)(table3.columns.contains("d"))
    assertResult(1)(table3.count())
  }

  "It" should "run with schemaHint passing via config" in {
    val tableName = "testSchema.table4"
    val config = ConfigUtils.getConfigFromJSON[JSONParserConfig]("""{"transformationConfig":{"event_timestamp_epoch":"ingested_timestamp"},"granularity":"days","schemaHint":"{\"type\":\"struct\",\"fields\":[{\"name\":\"a\",\"type\":\"integer\",\"nullable\":false,\"metadata\":{}},{\"name\":\"b\",\"type\":\"integer\",\"nullable\":false,\"metadata\":{}},{\"name\":\"c\",\"type\":{\"type\":\"struct\",\"fields\":[{\"name\":\"col1\",\"type\":\"integer\",\"nullable\":false,\"metadata\":{}},{\"name\":\"col2\",\"type\":\"integer\",\"nullable\":false,\"metadata\":{}},{\"name\":\"col3\",\"type\":\"integer\",\"nullable\":false,\"metadata\":{}}]},\"nullable\":false,\"metadata\":{}}]}"}""",formats = configFormats)
    val df2sql = "select 11 as a, 22 as b"
    val df2 = spark.sql(df2sql)
    writer.overwritePartition(df2, tableName)
    if (config.schemaHint.isDefined) {
      AutoParserImpl.spark = spark
      AutoParserImpl.schemaChangeBasedOnHint(config.schemaHint.get.trim, tableName, config.granularity.get)
    }
    val table = reader.read(tableName)
    assertResult(6)(table.columns.length) //including isValid,event_timestamp_ts, parsed_interval_timestamp_ts
  }

  "It" should "run without schemaHint in config" in {
    val tableName = "testSchema.table2"
    val config = ConfigUtils.getConfigFromJSON[JSONParserConfig]("""{"transformationConfig":{"event_timestamp_epoch":"ingested_timestamp"}}""",formats = configFormats)
    val df2sql = "select 11 as a, 22 as b, struct(1,2) as c,struct(1,2) as cc"
    val df2 = spark.sql(df2sql)
    writer.overwritePartition(df2, tableName)
    if (config.schemaHint.isDefined) {
      AutoParserImpl.spark = spark
      AutoParserImpl.schemaChangeBasedOnHint(config.schemaHint.get.trim, tableName, config.granularity.get)
    }
    val table = reader.read(tableName)
    assertResult(1)(table.count())
  }

  "it" should "replace special characters" in {

    val input = spark.read.json(getClass.getResource("/autoparserbase/input/input.json").getPath)
    val df1 = AutoParserImpl.specialCharacterReplace(spark, input)

    val expectedSchema = "{\"type\":\"struct\",\"fields\":[{\"name\":\"epoch__\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"isdeleted\",\"type\":\"boolean\",\"nullable\":true,\"metadata\":{}},{\"name\":\"job\",\"type\":{\"type\":\"struct\",\"fields\":[{\"name\":\"id\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"title\",\"type\":{\"type\":\"struct\",\"fields\":[{\"name\":\"__level__s\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"name\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}}]},\"nullable\":true,\"metadata\":{}}]},\"nullable\":true,\"metadata\":{}},{\"name\":\"person\",\"type\":{\"type\":\"array\",\"elementType\":{\"type\":\"struct\",\"fields\":[{\"name\":\"address\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"email__address\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}}]},\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"time__\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}}]}"
    assert(expectedSchema === df1.schema.json)

  }
  "it" should "add timestamp fields to the dataframe" in {
    val df = spark.read.json(getClass.getResource("/autoparserbase/input/test_data.json").getPath)
    val path1 = spark.read.json(getClass.getResource("/autoparserbase/input/config.json").getPath)
    val args = AutoParserImpl.getParseCmdArgs(Array("--config", s"file:$path1", "--modified-before", "2023-06-07 10:00:00", "--modified-after", "2023-06-07 09:00:00", "--rdm-path","s3a://bucket/qa/sds-autoparser/rdm/business_unit/","--srdm-table","srdm.business_unit"))
    val config = ConfigUtils.getConfigFromJSON[JSONParserConfig](jsonStr="""{"transformationConfig":[{"event_timestamp_epoch":"cast(event_timestamp as long)"}]}""",formats = configFormats)
    val output = AutoParserImpl.processDataFrame(df, args, config)
    val expected = spark.read.schema(output.schema).json(getClass.getResource("/autoparserbase/output/expected.json").getPath)
    val expectedDF = expected.drop("parsed_timestamp")
    val outputDF = output.drop("parsed_timestamp")
    val expectedData = expectedDF.withColumn("parsed_interval_timestamp", new Column(AssertNotNull(col("parsed_interval_timestamp").expr)))
    assertDataFrameDataEquals(outputDF.orderBy(expectedData.columns.map(col): _*), expectedData.orderBy(expectedData.columns.map(col): _*))

  }

  "it" should "partition data based on tenancy if tenancy column is present" in {
    val table= "test.table1"
    val df1 = spark.read.format("json").load(getClass.getResource("/autoparserbase/input/input2.json").getPath)
    val dfWithTenancy=df1.withColumn("event_timestamp_ts",to_timestamp(col("event_timestamp_ts"))).withColumn("parsed_interval_timestamp_ts",to_timestamp(col("parsed_interval_timestamp_ts")))
    AutoParserImpl.writeSRDM(spark,dfWithTenancy,table,"days")
    val partitionDetails=spark.sql(s"SELECT partition FROM iceberg_catalog.$table.partitions")
    assert(partitionDetails.count()==3)
    val describeOutput = spark.sql(s"DESCRIBE FORMATTED iceberg_catalog.$table")
    val partitionInfo = describeOutput
      .filter(describeOutput("col_name") === "Part 0")
    val initialPartition=partitionInfo.select("data_type").collect().map(_.getString(0))
    assert(initialPartition.contains("sds_tenancy_identifier"),"sds_tenancy_identifier is the initial partition")
  }

  "it" should "partition data based on isValid if tenancy is not provided " in {
    val tableName= "test.table2"
    val dataFrame = spark.read.format("json").load(getClass.getResource("/autoparserbase/input/input3.json").getPath)
    val dfWithoutTenancy= dataFrame.withColumn("event_timestamp_ts",to_timestamp(col("event_timestamp_ts"))).withColumn("parsed_interval_timestamp_ts",to_timestamp(col("parsed_interval_timestamp_ts")))
    AutoParserImpl.writeSRDM(spark,dfWithoutTenancy,tableName,"days")
    val dfWithTenancy = dfWithoutTenancy.withColumn("sds_tenancy_identifier",expr("'north'"))
    AutoParserImpl.writeSRDM(spark,dfWithTenancy,"test.table2","days")
    val describeOutput = spark.sql(s"DESCRIBE FORMATTED iceberg_catalog.$tableName")
    val partitionInfo = describeOutput
      .filter(describeOutput("col_name") === "Part 0")
    val initialPartition=partitionInfo.select("data_type").collect().map(_.getString(0))
    assert(initialPartition.contains("isValid"),"isValid is the initial partition")
  }

}

private object AutoParserImpl extends AutoParserBase[AutoParserJobArgs, JSONParserConfig] {
  override def errorColumn: String = "_corrupt_record"

  override def parse(spark: SparkSession, dataFrame: DataFrame, config: JSONParserConfig): DataFrame = ???

  /** Must be implemented in concrete class to specify Configuration class
   *
   * @example
   * override def getConfigManifest:Manifest[ConfigClass] = manifest[ConfigClass]
   */
  override def getConfigManifest: Manifest[JSONParserConfig] = ???

}