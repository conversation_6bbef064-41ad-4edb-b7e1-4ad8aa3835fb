package isolated.ai.prevalent.parserimpl.utils

import ai.prevalent.sdsautoparser.utils.AutoParserDateUtils._
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec

import java.time.zone.ZoneRulesException
import java.time.{DateTimeException, ZoneId, ZonedDateTime}

class AutoParserDateUtilsSpec extends AnyFlatSpec with DataFrameSuiteBase with BeforeAndAfter{
  var epoch1:Long=_
  var timezone:String=_
  var pattern1:String=_



  before{
    spark.sparkContext.setLogLevel("ERROR")
    epoch1 = 1635637200000L
    timezone = "Asia/Kolkata"
    pattern1 = "yyyy-MM-dd HH:mm:ss.SSSXXX"
  }

  "getDateTimeFromEpoch" should "give the correct date from epoch" in {

    val expected = ZonedDateTime.of(2021, 10, 31, 5, 10, 0, 0, ZoneId.of(timezone))
    assert(getDateTimeFromEpoch(epoch1, timezone) == expected)
  }

"getDateTimeFromEpoch" should "throw an exception if incorrect date format is given" in{
    val timezone2 = "Invalid/Timezone"
    assertThrows[DateTimeException](getDateTimeFromEpoch(epoch1, timezone2))
  }

  "getIsoDateFromEpoch" should "give correct iso date from given epoch" in {
    val output = getIsoDateFromEpoch(epoch1, pattern1, timezone)
    val expected = "2021-10-31 05:10:00.000+05:30"
    assert(output === expected)
  }
"getIsoDateFromEpoch" should "throw an exception whe an invalid pattern is given" in {
  val invalidPattern = "invalid pattern"
  val timezone_2 = "America/Los_Angeles"
  assertThrows[IllegalArgumentException] {
    getIsoDateFromEpoch(epoch1, invalidPattern, timezone_2)
  }
}
  "getIsoDateFromEpoch" should "throw an exception whe an invalid timezone is given" in{
    val timezone_3 = "Invalid/Timezone"
    assertThrows[ZoneRulesException] {
      getIsoDateFromEpoch(epoch1, pattern1, timezone_3)
    }

  }
  "getOffsetFromEpoch" should "give offset from the provided epoch" in {

    val output = getOffsetFromEpoch(epoch1, timezone)
    val expected = "+05:30"
    assert(expected === output)
  }
  "getOffsetFromEpoch" should "throw an exception when invalid timezone is given" in{
    assertThrows[ZoneRulesException] {
      getOffsetFromEpoch(epoch1, "invalid/timezone")
    }
  }

  "convertTimeStampStringToDate" should "convert timestamp string to datetime" in{
    val timestamp = "2021-01-01T00:00:00.000"
    val expectedDateTime = new DateTime(timestamp, DateTimeZone.UTC)
    val resultDateTime = convertTimeStampStringToDate(timestamp)
    assert(expectedDateTime===resultDateTime)
  }

  "getYearMonthDayHour" should "return array of year,month,day" in{
    val timestamp = new DateTime(2022, 10, 1, 14, 30, 0)
    val result = getYearMonthDayHour(timestamp)
    val expected=Array(2022, 10, 1, 14)
    assert(result sameElements expected)

  }


}
