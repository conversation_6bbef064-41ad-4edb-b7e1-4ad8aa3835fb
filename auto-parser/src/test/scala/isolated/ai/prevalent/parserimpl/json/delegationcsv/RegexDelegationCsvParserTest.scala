package isolated.ai.prevalent.parserimpl.json.delegationcsv

import ai.prevalent.sdsautoparser.parserimpl.regex.delegationcsv.RegexDelegationCSVParser
import ai.prevalent.sdsautoparser.parserimpl.regex.delegationcsv.config.RegexDelegationCSVParserConfig
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.apache.spark.SparkConf
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec


class RegexDelegationCsvParserTest extends AnyFlatSpec with DataFrameSuiteBase with BeforeAndAfter {

  var config1: RegexDelegationCSVParserConfig = _
  var configs: Map[String, RegexDelegationCSVParserConfig] = _
  var configPaths: List[String] = _
  var inputFiles: List[String] = _
  var params: Array[String] = _

  before {
    spark.sparkContext.setLogLevel("ERROR")
    inputFiles = List("config1.json")
    params = Array("--spark-service", "spark","--rdm-path","s3a://sds-qa-datalake/ingestion/rdm/nexus/rsa_syslog/","--srdm-table","srdm.nexus__rsa_syslog", "--modified-before", "2023-06-07 12:00:00", "--modified-after", "2023-06-07 11:00:00", "--config-path")
    configs = inputFiles.map { inputFile =>
      val path = getClass.getResource(s"/parserimpl/regex/delegationcsv/input/$inputFile").getPath
      val config = RegexDelegationCSVParser.getConfig(spark, RegexDelegationCSVParser.getParseCmdArgs(params :+ s"file:$path"))
      (inputFile, config)
    }.toMap
    config1 = configs("config1.json")


  }

  override def conf: SparkConf = super.conf.set("spark.sql.session.timeZone", "Asia/Kolkata")

  "it" should "parse single record of regex data" in {
    val df = spark.read.text(getClass.getResource("/parserimpl/regex/delegationcsv/input/input1.txt").getPath).withColumnRenamed("value", "payload")
    val outputDf=RegexDelegationCSVParser.parse(spark,df,config1)
    val expectedDf = spark.read.schema(outputDf.drop("payload").schema).json(getClass.getResource("/parserimpl/regex/delegationcsv/output/single_record.json").getPath)
    assertDataFrameDataEquals(expectedDf, outputDf.drop("payload"))

  }

}

