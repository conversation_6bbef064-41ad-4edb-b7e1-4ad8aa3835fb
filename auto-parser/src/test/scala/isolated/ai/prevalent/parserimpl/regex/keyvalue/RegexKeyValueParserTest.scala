package isolated.ai.prevalent.parserimpl.regex.keyvalue

import ai.prevalent.sdsautoparser.parserimpl.regex.keyvalue.RegexKeyValueParser
import ai.prevalent.sdsautoparser.parserimpl.regex.keyvalue.config.RegexKeyValueParserConfig
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.apache.spark.SparkConf
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec


class RegexKeyValueParserTest extends AnyFlatSpec with DataFrameSuiteBase with BeforeAndAfter {

  var config1: RegexKeyValueParserConfig = _
  var config2: RegexKeyValueParserConfig = _
  var config3: RegexKeyValueParserConfig = _
  var config4: RegexKeyValueParserConfig = _
  var config5: RegexKeyValueParserConfig = _

  var configs: Map[String, RegexKeyValueParserConfig] = _
  var configPaths: List[String] = _
  var inputFiles: List[String] = _
  var params: Array[String] = _


  before {
    spark.sparkContext.setLogLevel("ERROR")
    inputFiles = List("config1.json", "config2.json", "config3.json", "config4.json", "config5.json")
    params = Array("--spark-service", "spark","--rdm-path","s3a://sds-qa-datalake/ingestion/rdm/qualys/host_summary/","--srdm-table","srdm.qualys__host_summary", "--modified-before", "2023-06-07 12:00:00", "--modified-after", "2023-06-07 11:00:00", "--config-path")
    configs = inputFiles.map { inputFile =>
      val path = getClass.getResource(s"/parserimpl/regex/keyvalue/input/$inputFile").getPath
      val config = RegexKeyValueParser.getConfig(spark, RegexKeyValueParser.getParseCmdArgs(params :+ s"file:$path"))
      (inputFile, config)
    }.toMap
    config1 = configs("config1.json")
    config2 = configs("config2.json")
    config3 = configs("config3.json")
    config4 = configs("config4.json")
    config5 = configs("config5.json")


  }

  override def conf: SparkConf = super.conf.set("spark.sql.session.timeZone", "Asia/Kolkata")

  "it" should "parse single record of regex data" in {
    val df = spark.read.text(getClass.getResource("/parserimpl/regex/keyvalue/input/input.txt").getPath).withColumnRenamed("value", "payload")
    val outputDF = RegexKeyValueParser.applyCustomRegexExtraction(df, config1)
    val expectedDf = spark.read.json(getClass.getResource("/parserimpl/regex/keyvalue/output/output.json").getPath)
    assertDataFrameDataEquals(expectedDf, outputDF)
  }

  "it" should "parse key value data with special characters" in {
    val df = spark.read.text(getClass.getResource("/parserimpl/regex/keyvalue/input/input2.txt").getPath).withColumnRenamed("value", "payload")
    val outputDF = RegexKeyValueParser.applyCustomRegexExtraction(df, config2)
    val expectedDf = spark.read.json(getClass.getResource("/parserimpl/regex/keyvalue/output/output2.json").getPath)
    assertDataFrameDataEquals(expectedDf, outputDF)
  }

  "it" should "parse key value data with different data types" in {
    //parses long,string,boolean and timestamp data
    val df = spark.read.text(getClass.getResource("/parserimpl/regex/keyvalue/input/input3.txt").getPath).withColumnRenamed("value", "payload")
    val outputDF = RegexKeyValueParser.applyCustomRegexExtraction(df, config3)
    val expected = spark.read.schema(outputDF.schema).json(getClass.getResource("/parserimpl/regex/keyvalue/output/output3.json").getPath)
    assertDataFrameDataEquals(expected, outputDF)
  }
  "it" should "trim trailing white spaces in the value" in {
    val df = spark.read.text(getClass.getResource("/parserimpl/regex/keyvalue/input/input4.txt").getPath).withColumnRenamed("value", "payload")
    val outputDF = RegexKeyValueParser.applyCustomRegexExtraction(df, config4)
    val expected = spark.read.json(getClass.getResource("/parserimpl/regex/keyvalue/output/output4.json").getPath)
    assertDataFrameDataEquals(outputDF, expected)
  }
  "it" should "parse key value data with splitter in the value" in {

    val df = spark.read.text(getClass.getResource("/parserimpl/regex/keyvalue/input/input5.txt").getPath).withColumnRenamed("value", "payload")
    val outputDF = RegexKeyValueParser.applyCustomRegexExtraction(df, config5)
    val expected = "Windows Server =2012 R2 Standard =64 bit Edition"
    assert(outputDF.select("os").head().get(0) === expected)
  }
  "it" should "parse multiple records in the given data" in {
    val df = spark.read.text(getClass.getResource("/parserimpl/regex/keyvalue/input/input6.txt").getPath).withColumnRenamed("value", "payload")
    val outputDF = RegexKeyValueParser.applyCustomRegexExtraction(df, config4)
    val expectedDf = spark.read.json(getClass.getResource("/parserimpl/regex/keyvalue/output/output6.json").getPath)
    assertDataFrameDataEquals(expectedDf, outputDF)
  }


}
