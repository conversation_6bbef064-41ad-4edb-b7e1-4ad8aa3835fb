package isolated.ai.prevalent.parserimpl.utils

import ai.prevalent.sdsautoparser.utils.AutoParserSparkUtils._
import ai.prevalent.sdspecore.sparkbase.table.iceberg.SDSIcebergConnect
import ai.prevalent.sdspecore.sparkbase.table.{SDSTableReader, SDSTableReaderFactory, SDSTableWriter, SDSTableWriterFactory}
import ai.prevalent.sdspecore.test.utils.IcebergSparkTestWrapper
import com.holdenkarau.spark.testing.DataFrameSuiteBase
import org.apache.spark.SparkConf
import org.apache.spark.sql.functions.{col, expr}
import org.apache.spark.sql.types._
import org.apache.spark.sql.{DataFrame, Row}
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec

import java.io.File
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import scala.reflect.io.Directory


class AutoParserSparkUtilsSpec extends AnyFlatSpec with DataFrameSuiteBase with IcebergSparkTestWrapper with BeforeAndAfter {

  override def warehousePath: String = {
    new Directory(new File(s"${getClass.getResource("/").getPath}/iceberg_warehouse")).createDirectory()
    getClass.getResource("/iceberg_warehouse").getPath
  }


  import sqlContext.implicits._

  var baseCols: Seq[String] = _
  var errorCol: Seq[String] = _
  var df1: DataFrame = _
  var inputPath: String = _
  var df2: DataFrame = _
  var df3: DataFrame = _
  var testData: Seq[Row] = _
  var testsql: String = _
  var testSchema: StructType = _
  var writer: SDSTableWriter = _
  var reader: SDSTableReader = _
  before {
    spark.sparkContext.setLogLevel("ERROR")
    baseCols = Seq("a", "b", "c")
    errorCol = Seq("error")
    df1 = Seq((1, "a", true), (2, "b", false)).toDF("id", "name", "flag")
    inputPath = getClass.getResource("/autoparserbase/input/test_data.avro").getPath
    df2 = spark.read.json(getClass.getResource("/AutoparserUtil/input/autoparser_util_test.json").getPath)
    df3 = spark.createDataFrame(Seq(
      (1, "2021-01-01T01:01:01Z"),
      (2, "2023-02-02T02:02:02Z")
    )).toDF("id", "timestamp")
    testData = Seq(Row(1, "1687098942000", "2022-01-01T01:00:00Z"))
    testsql = """{"type":"struct","fields":[{"name":"id","type":"integer","nullable":true},{"name":"event_timestamp","type":"string","nullable":true},{"name":"parsed_interval_timestamp","type":"string","nullable":true}]}"""
    testSchema = DataType.fromJson(testsql).asInstanceOf[StructType]
    writer = SDSTableWriterFactory.get(SDSIcebergConnect.name, spark)
    reader = SDSTableReaderFactory.get(SDSIcebergConnect.name, spark)

  }
  after {
    new Directory(new File(warehousePath)).deleteRecursively()
  }


  override def conf: SparkConf = super.conf.set("spark.sql.iceberg.merge-schema", "true")
    .set("spark.sql.iceberg.set-all-nullable-field", "true")
    .set("spark.sql.iceberg.check-ordering", "false")
    .set("spark.sql.session.timeZone","Asia/Kolkata")


  "change schema  method" should "return schema with special characters replaced" in {
    val structureSchema = new StructType().add("nam###e", StringType).add("booksInte@rsted", ArrayType(new StructType().add("name ", StringType).add("author  ", StringType).add("page^^s", IntegerType)))
    val reqStructureSchema = new StructType().add("nam______e", StringType).add("booksInte__rsted", ArrayType(new StructType().add("name__", StringType).add("author____", StringType).add("page____s", IntegerType)))
    val newSchema = structureSchema.fields.foldLeft(new StructType())((schma, field) => {
      changeSchema(field, schma)
    })
    assert(newSchema == reqStructureSchema)

  }


  "change schema  method" should "return schema with special characters replaced when array is empty" in {
    val structureSchema = new StructType().add("aeee!!e1", ArrayType(NullType)).add("na!!1me", StringType)
    val reqSchema = new StructType().add("aeee____e1", ArrayType(NullType)).add("na____1me", StringType)
    val newSchema = structureSchema.fields.foldLeft(new StructType())((schma, field) => {
      changeSchema(field, schma)
    })
    assert(newSchema == reqSchema)

  }

  "addDataFrameValidityField" should "add all isValid column correctly on invalid dataframe" in {

    val columns = baseCols ++ errorCol
    val inputData = List[(Int, Int, Int, Option[Int])]((1, 2, 3, Some(0)), (4, 5, 6, None), (7, 8, 9, None))
    val dataFrame = sc.parallelize(inputData).toDF(columns: _*)
    val outputDF = addDataFrameValidityField(dataFrame, errorCol.toArray, Array("b", "c"))
    val expectedData = List[(Int, Int, Int, Option[Int], Boolean)]((1, 2, 3, Some(0), false), (4, 5, 6, None, true), (7, 8, 9, None, true))
    val expectedDF = sc.parallelize(expectedData).toDF(columns :+ "isValid": _*)
    assertDataFrameDataEquals(outputDF, expectedDF)
  }

  "addDataFrameValidityField" should "add all isValid column correctly on valid dataframe with error columns absent" in {
    val inputData = List[(Int, Int, Int)]((1, 2, 3), (4, 5, 6), (7, 8, 9))
    val dataFrame = sc.parallelize(inputData).toDF(baseCols: _*)
    val outputDF = addDataFrameValidityField(dataFrame, errorCol.toArray, Array("b", "c"))
    val expectedData = List[(Int, Int, Int, Option[Int], Boolean)]((1, 2, 3, None, true), (4, 5, 6, None, true), (7, 8, 9, None, true))
    val expectedDF = sc.parallelize(expectedData).toDF(baseCols ++ List("error", "isValid"): _*)
    assertDataFrameDataEquals(outputDF, expectedDF)
  }

  "addDataFrameValidityField" should "add all records to isValid=false on transformed fields absent" in {
    val inputData = List[(Int, Int, Int)]((1, 2, 3), (4, 5, 6), (7, 8, 9))
    val dataFrame = sc.parallelize(inputData).toDF(baseCols: _*)
    val outputDF = addDataFrameValidityField(dataFrame, errorCol.toArray, Array("d", "e"))
    val expectedData = List[(Int, Int, Int, Option[Int], Option[Int], Option[Int], Boolean)]((1, 2, 3, None, None, None, false), (4, 5, 6, None, None, None, false), (7, 8, 9, None, None, None, false))
    val expectedDF = sc.parallelize(expectedData).toDF(baseCols ++ List("d", "e", "error", "isValid"): _*)
    assertDataFrameDataEquals(outputDF, expectedDF)
  }

  "addDataFrameValidityField" should "add all fields to isValid=false on all records corrupt" in {
    val columns = List("a", "b", "c", "error")
    val inputData = List[(Int, Int, Int, Int)]((1, 2, 3, 0), (4, 5, 6, 0), (7, 8, 9, 0))
    val dataFrame = sc.parallelize(inputData).toDF(columns: _*)
    val transformedCols = Array("d", "e")
    val outputDF = addDataFrameValidityField(dataFrame, errorCol.toArray, transformedCols)
    val expectedData = List[(Int, Int, Int, Int, Option[Int], Option[Int], Boolean)]((1, 2, 3, 0, None, None, false), (4, 5, 6, 0, None, None, false), (7, 8, 9, 0, None, None, false))
    val expectedDF = sc.parallelize(expectedData).toDF(columns ++ List("d", "e", "isValid"): _*)
    assertDataFrameDataEquals(outputDF, expectedDF)
  }

  "addAuditFields" should "add timestamp fields" in {

    val testDF = spark.createDataFrame(spark.sparkContext.parallelize(testData), testSchema).withColumn("event_timestamp_epoch", expr("cast(event_timestamp as long)"))
    val resultDF = addAuditFields(testDF, "event_timestamp", "parsed_interval_timestamp", "Asia/Kolkata")
    val expectedDf = spark.read.schema(resultDF.schema).format("json").schema(resultDF.schema).load(getClass.getResource("/AutoparserUtil/output/date.json").getPath)
    assertDataFrameDataEquals(resultDF.orderBy(expectedDf.columns.map(col): _*), expectedDf.orderBy(expectedDf.columns.map(col): _*))
  }

  "dropColumns" should "drop the specified columns" in {

    val columns = Array("name", "flag")
    val result = dropColumns(df1, columns)
    assert(result.columns === Array("id"))
  }
  "dropColumns" should "return the original DataFrame if no columns are specified" in {
    val columnsTest = Array("")
    val resultTest = dropColumns(df1, columnsTest)
    assert(resultTest.columns === Array("id", "name", "flag"))
  }
  "dropColumns" should "throw an exception if the DataFrame is null" in {

    val df1: DataFrame = null
    val colTest = Array("name", "flag")
    assertThrows[NullPointerException] {
      dropColumns(df1, colTest)
    }
  }
  "dropColumns" should "not drop columns if the column doesnt exist" in {
    val columnT = Array("age", "gender")
    val result_1 = dropColumns(df1, columnT)
    assert(result_1.columns === Array("id", "name", "flag"))
  }
  "addColumnsToDataFrame" should "add columns to the dataframe" in {
    val inputDF = Seq((1, "a"), (2, "b")).toDF("id", "name")
    val expectedCols = Array("id", "name", "age", "gender")
    val expectedDF = Seq((1, "a", null, null), (2, "b", null, null)).toDF(expectedCols: _*)
    val resultDF = addColumnsToDataFrame(inputDF, expectedCols)

    assert(expectedDF.collect() === resultDF.collect())
  }

  "applyTransformations" should "should return the input dataframe when transformation is empty" in {
    val inputDF = spark.range(10).toDF()
    val transformationConfig = List.empty[Map[String, String]]
    val outputDF = applyTransformations(inputDF, transformationConfig)
    assert(outputDF.collect() === inputDF.collect())
  }

  "applyTransformations" should "should apply transformation in the transformation config" in {
    val transformation_Config = List(Map("isdeleted" -> "cast(isdeleted as string)"))
    val outputDF1 = applyTransformations(df2, transformation_Config)
    assert(outputDF1.columns sameElements Array("epoch", "isdeleted", "person", "time"))
  }

  "applyTransformations" should "should apply multiple transformation in the transformation config" in {
    val transformation_Config_1 = List(Map("deleted" -> "cast(isdeleted as string)"),Map( "host" -> "transform(person,x->to_json(x))"), Map("event_timestamp_epoch" -> "cast(epoch as long)"))
    val outputDF2 = applyTransformations(df2, transformation_Config_1)
    val expectedDf = spark.read.json(getClass.getResource("/AutoparserUtil/output/autoparser_util_test.json").getPath)
    assertDataFrameDataEquals(expectedDf.orderBy("time"), outputDF2.orderBy("time"))
  }

  "applyTransformations" should "should throw an exception when input transformation is invalid expression" in {
    val input_DF = spark.range(10).toDF()
    val transformation_Config_2 = List(Map("id_squared" -> "id * "))
    assertThrows[org.apache.spark.sql.AnalysisException] {
      applyTransformations(input_DF, transformation_Config_2)
    }
  }
  "applyTransformations" should "should apply multiple transformations" in {
    val testDF = spark.createDataFrame(spark.sparkContext.parallelize(testData), testSchema).withColumn("event_timestamp_epoch", expr("cast(event_timestamp as long)"))
    val transformationConfig = List(Map("event_timestamp_ts" ->"to_timestamp(parsed_interval_timestamp, \"yyyy-MM-dd'T'HH:mm:ss'Z'\")"), Map("event_timestamp_date" -> "to_date(event_timestamp_ts,'yyyy-MM-dd')"), Map("event_timestamp_year" -> "year(event_timestamp_date)"),  Map("next_year" -> "event_timestamp_year+1"))
    val outputDF = applyTransformations(testDF, transformationConfig)
    val expected = spark.read.schema(outputDF.schema).json(getClass.getResource("/AutoparserUtil/output/output.json").getPath)
    assertDataFrameDataEquals(outputDF, expected)
  }

  "changeSchema" should "change schema to produce valid column names for struct data" in {
    val structureSchema = new StructType()
      .add("name", new StructType()
        .add(" first name", StringType)
        .add("middle_name", StringType)
        .add("lastname:", StringType))
    val newSchema1 = new StructType()
      .add("name", new StructType()
        .add(" first name", StringType)
        .add("middle_name", StringType)
        .add("lastname:", StringType))
      .add("name", new StructType()
        .add("__first__name", StringType)
        .add("middle_name", StringType)
        .add("lastname__", StringType))
    val structureData = Seq(
      Row(Row("James ", "", "Smith"), "36636", "M", 3100))
    val df = spark.createDataFrame(spark.sparkContext.parallelize(structureData), structureSchema)
    val schema1 = changeSchema(df.schema.fields(0), df.schema)
    assert(schema1 === newSchema1)
  }

  "changeSchema" should "change schema to produce valid column names for array data" in {
    val simpleSchema = StructType(Array(
      StructField("first name", StringType, nullable = true),
      StructField("middle name", StringType, nullable = true)
    ))
    val newSchema2 = StructType(Array(
      StructField("first name", StringType, nullable = true),
      StructField("middle name", StringType, nullable = true),
      StructField("first__name", StringType, nullable = true)
    ))
    val schema2 = changeSchema(simpleSchema.fields(0), simpleSchema)
    assert(schema2 === newSchema2)

  }

  "createTimeStampStringColumn" should "add timestamp column" in {

    //it should add timestamp if correct column name and expression are specified
    val result = createTimeStampStringColumn(df3, "timestamp_string", "to_timestamp(timestamp)")
    val schema = StructType(Seq(StructField("id", IntegerType, nullable = false),
      StructField("timestamp", StringType, nullable = true), StructField("timestamp_string", StringType, nullable = true)))
    val expected = spark.createDataFrame(spark.sparkContext.parallelize(Seq(
      Row(1, "2021-01-01T01:01:01Z", "2021-01-01 06:31:01"),
      Row(2, "2023-02-02T02:02:02Z", "2023-02-02 07:32:02"))
    ), schema).withColumn("timestamp_string", expr("to_timestamp(timestamp_string)"))
    assert(result.schema === expected.schema)
    assert(result.collect().sameElements(expected.collect()))
  }
  "createTimeStampStringColumn" should "throw exception if invalid expression is provided" in {
    assertThrows[org.apache.spark.sql.AnalysisException] {
      createTimeStampStringColumn(df3, "timestamp_string", "invalid_expression")
    }
  }
  "addDateFieldsFromTimestamp" should "add date fields from timestamp" in {
    val data = Seq(
      Row("2021-01-01 12:00:00"),
      Row("2021-02-01 13:00:00"),
      Row("2021-03-01 14:00:00")
    )
    val schema = StructType(Seq(StructField("event_timestamp", StringType, nullable = false)))
    val df = spark.createDataFrame(spark.sparkContext.parallelize(data), schema).toDF("event_timestamp").withColumn("event_timestamp", expr("cast(event_timestamp as date)"))
    val result = addDateFieldsFromTimestamp(df, "event_timestamp")

    assert(result.columns.contains("event_timestamp_year"))
    assert(result.columns.contains("event_timestamp_month"))
    assert(result.columns.contains("event_timestamp_day"))
    assert(result.columns.contains("event_timestamp_hour"))
    assert(result.count() == 3)
  }

  "readRDMData" should "read rdm data in the specified time interval" in {
    val modifiedBefore = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"))
    val modifiedAfter = "2023-06-01T00:00:00Z"
    val result = readRDMData(spark, inputPath, modifiedAfter, modifiedBefore)
    assert(result.count() > 0)
  }

  "readRDMData" should "return empty dataframe if no data is present in the interval" in {
    val modified_After = "2022-08-01T00:00:00Z"
    val modified_Before = "2022-06-16T00:00:00Z"
    val result_1 = readRDMData(spark, inputPath, modified_After, modified_Before)
    assert(result_1.count() == 0)
  }

  "compareSchemas" should "return empty for equal structs" in {
    val right = spark.sql("select 1 as a, 2 as b, 'cc' as c, struct(1,2,'dd') as d, array('a','b','c') as e").schema
    val (missing, extra, mismatch) = compareSchemas(left = right, right=right)
    assert(missing.isEmpty)
    assert(extra.isEmpty)
    assert(mismatch.isEmpty)
  }

  "compareSchemas" should "return results for nested structs" in {
    val right = spark.sql("select 1 as a,1 as aa, 2 as b, 'cc' as c, struct(1,2,'dd') as d, array('a','b','c') as e, array(struct(1,'aa')) as f, struct(array(1,2)) as g").schema
    val left = spark.sql("select '1' as a, 2 as b,2 as bb, 'cc' as c, struct(1,'2','dd') as d, array(1,2,3) as e, array(struct(1.2)) as f, struct('a') as g").schema
    val (missing, extra, mismatch) = compareSchemas(left = left, right = right)
    assert(missing sameElements Array("bb"))
    assert(extra sameElements Array("aa","f.col2"))
    assert(mismatch sameElements Array("a: StringType (SRDM) vs IntegerType (input data)", "d.col2: StringType (SRDM) vs IntegerType (input data)", "e: IntegerType (SRDM) vs StringType (input data)", "f.col1: DecimalType(2,1) (SRDM) vs IntegerType (input data)","g.col1: StringType (SRDM) vs ArrayType(IntegerType,false) (input data)"))
  }

}

